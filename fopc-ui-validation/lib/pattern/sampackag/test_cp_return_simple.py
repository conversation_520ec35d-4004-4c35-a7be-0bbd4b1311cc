#!/usr/bin/env python3
"""
Simple test for CP Return Rate using actual CSV data
"""

import pandas as pd
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_cp_return_rate_with_csv():
    """Test CP Return Rate calculation with actual CSV data"""
    
    print("=== Testing CP Return Rate with CSV Data ===\n")
    
    try:
        # Read the CSV data
        print("1. Loading CSV data...")
        df = pd.read_csv('raw-data/all_revenue_details.csv', na_values=[], keep_default_na=False)
        print(f"   Total records loaded: {len(df)}")
        print(f"   Columns: {list(df.columns)}")
        
        # Check data structure
        print(f"\n2. Data Analysis:")
        print(f"   VIN column exists: {'vin' in df.columns}")
        print(f"   Unique VINs: {df['vin'].nunique()}")
        print(f"   Date range: {df['closeddate'].min()} to {df['closeddate'].max()}")
        print(f"   Departments: {df['department'].unique()}")
        print(f"   Paytypegroups: {df['paytypegroup'].unique()}")
        print(f"   Hide_ro values: {df['hide_ro'].unique()}")
        
        # Filter for Service department
        service_data = df[df['department'] == 'Service']
        print(f"   Service records: {len(service_data)}")
        
        # Filter for non-hidden ROs
        visible_data = service_data[service_data['hide_ro'] == '0']
        print(f"   Visible RO records: {len(visible_data)}")
        
        # Filter for customer pay types
        customer_pay_types = {'C', 'E', 'M'}
        cp_data = visible_data[visible_data['paytypegroup'].isin(customer_pay_types)]
        print(f"   Customer pay records: {len(cp_data)}")
        
        # Filter for non-N/A opcategory
        valid_ops = cp_data[cp_data['opcategory'] != 'N/A']
        print(f"   Valid opcategory records: {len(valid_ops)}")
        
        # Filter for non-zero revenue/cost
        non_zero = valid_ops[
            ~((valid_ops['lbrsale'].astype(float) == 0) &
              (valid_ops['lbrsoldhours'].astype(float) == 0) &
              (valid_ops['prtextendedsale'].astype(float) == 0) &
              (valid_ops['prtextendedcost'].astype(float) == 0))
        ]
        print(f"   Non-zero revenue/cost records: {len(non_zero)}")
        print(f"   Unique VINs in filtered data: {non_zero['vin'].nunique()}")
        
        if len(non_zero) == 0:
            print("\n❌ No records pass all filters - this explains the zero return rate!")
            print("\nTroubleshooting:")
            print("- Check if revenue/cost columns have the right data types")
            print("- Verify opcategory values are not all 'N/A'")
            print("- Ensure paytypegroup has customer pay values")
            return
        
        # Convert dates and check date range
        non_zero = non_zero.copy()
        non_zero['closeddate'] = pd.to_datetime(non_zero['closeddate'])
        print(f"   Date range after filtering: {non_zero['closeddate'].min()} to {non_zero['closeddate'].max()}")
        
        # Sample some data
        print(f"\n3. Sample filtered data:")
        sample_data = non_zero[['ronumber', 'closeddate', 'vin', 'paytypegroup', 'opcategory', 'lbrsale']].head()
        print(sample_data.to_string(index=False))
        
        # Now test the actual CP Return Rate function
        print(f"\n4. Testing CP Return Rate function...")
        
        # Import the function
        from validate_special_metrics import calculate_cp_return_rate
        
        # Test with the filtered data
        customer_pay_types = {'C', 'E', 'M'}
        result = calculate_cp_return_rate(non_zero, advisor=None, customer_pay_types=customer_pay_types)
        
        print(f"\n5. CP Return Rate Result:")
        print(f"   Result type: {type(result)}")
        
        if 'json_data' in result:
            datasets = result['json_data'][0]['datasets']
            labels = result['json_data'][0]['labels']
            
            print(f"   Labels: {labels}")
            print(f"   12-month rates: {datasets[0]['data']}")
            print(f"   6-month rates: {datasets[1]['data']}")
            
            # Check if we got actual values
            twelve_month_data = datasets[0]['data']
            six_month_data = datasets[1]['data']
            
            if all(rate == '0.00' for rate in twelve_month_data + six_month_data):
                print("\n❌ Still getting zero values - need to debug further")
            else:
                print("\n✅ Got non-zero return rates!")
        else:
            print(f"   Unexpected result format: {result}")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_cp_return_rate_with_csv()
