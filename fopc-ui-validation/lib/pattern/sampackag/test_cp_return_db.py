#!/usr/bin/env python3
"""
Test CP Return Rate database integration
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_cp_return_rate_db():
    """Test CP Return Rate database integration"""
    
    print("=== Testing CP Return Rate Database Integration ===\n")
    
    try:
        # Test the database query handler
        print("1. Testing CP Return Rate Query Handler...")
        from db_handler.db_query_handler import cpReturnRateTableQuery
        
        query_handler = cpReturnRateTableQuery()
        
        # Test with different advisor configurations
        test_cases = [
            None,
            ['All'],
            ['<PERSON>'],
            ['<PERSON>', '<PERSON>']
        ]
        
        for i, advisor in enumerate(test_cases):
            print(f"   Test case {i+1}: advisor = {advisor}")
            query = query_handler.generate_query(advisor)
            print(f"   Generated query: {query}")
            print()
        
        # Test the database connector
        print("2. Testing CP Return Rate Database Connector...")
        from db_handler.db_connector import cpReturnRateTable
        
        cp_return_db = cpReturnRateTable()
        print("   Database connector created successfully")
        
        # Note: We can't actually test the database connection without proper credentials
        # But we can test the structure
        
        # Test the main function
        print("3. Testing CP Return Rate Main Function...")
        from validate_special_metrics import calculate_cp_return_rate
        
        print("   Testing with advisor = None...")
        result = calculate_cp_return_rate(advisor=None)
        print(f"   Result type: {type(result)}")
        print(f"   Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        if isinstance(result, dict) and 'json_data' in result:
            json_data = result['json_data']
            if json_data and len(json_data) > 0:
                datasets = json_data[0].get('datasets', [])
                labels = json_data[0].get('labels', [])
                print(f"   Datasets count: {len(datasets)}")
                print(f"   Labels: {labels}")
                
                for dataset in datasets:
                    label = dataset.get('label', 'Unknown')
                    data = dataset.get('data', [])
                    print(f"   {label}: {data}")
            else:
                print("   No json_data found")
        else:
            print(f"   Unexpected result format: {result}")
        
        print("\n4. Testing CP Return Rate Summary Function...")
        from validate_special_metrics import get_cp_return_rate_summary
        
        summary = get_cp_return_rate_summary(None, advisor=None, target_month='2023-11')
        print(f"   Summary result: {summary}")
        
        print("\n✅ CP Return Rate database integration test completed!")
        print("\nNOTE: If you're getting zero values, it could be due to:")
        print("1. Database connection issues")
        print("2. No data in the database for the specified period")
        print("3. Database function returning empty results")
        print("4. Environment variables not set correctly")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all required modules are available")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_database_function_call():
    """Test the actual database function call format"""
    
    print("\n=== Database Function Call Format Test ===\n")
    
    print("The database function expects:")
    print("  stateless_dbd_special_metrics.get_returnrate_by_service_advisor(advisor_array)")
    print()
    print("Where advisor_array is a PostgreSQL array like:")
    print("  ARRAY['All'] for all advisors")
    print("  ARRAY['John Doe'] for specific advisor")
    print("  ARRAY['John Doe', 'Jane Smith'] for multiple advisors")
    print()
    
    # Test query generation
    from db_handler.db_query_handler import cpReturnRateTableQuery
    
    query_handler = cpReturnRateTableQuery()
    
    test_cases = [
        (None, "All advisors (None)"),
        (['All'], "All advisors (explicit)"),
        (['John Doe'], "Single advisor"),
        (['John Doe', 'Jane Smith'], "Multiple advisors")
    ]
    
    for advisor, description in test_cases:
        print(f"{description}:")
        query = query_handler.generate_query(advisor)
        print(f"  Query: {query}")
        print()

if __name__ == "__main__":
    test_cp_return_rate_db()
    test_database_function_call()
