/home/<USER>/Downloads/python db Repo Changes/fopc-ui-validation (6)/fopc-ui-validation/fopc-ui-validation/lib/pattern/sampackag/validate_special_metrics.py:481: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  filtered_df_with_sales['min_opendate'] = filtered_df_with_sales.groupby('unique_ro_number')['opendate'].transform('min')
/home/<USER>/Downloads/python db Repo Changes/fopc-ui-validation (6)/fopc-ui-validation/fopc-ui-validation/lib/pattern/sampackag/validate_special_metrics.py:482: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  filtered_df_with_sales['open_days'] = (pd.to_datetime(filtered_df_with_sales['closeddate']) - pd.to_datetime(filtered_df_with_sales['min_opendate'])).dt.days
/home/<USER>/Downloads/python db Repo Changes/fopc-ui-validation (6)/fopc-ui-validation/fopc-ui-validation/lib/pattern/sampackag/validate_special_metrics.py:575: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  mpi_opportunities['unique_ronumber'] = mpi_opportunities['ronumber'].astype(str) + '_' + mpi_opportunities['closeddate'].astype(str)
/home/<USER>/Downloads/python db Repo Changes/fopc-ui-validation (6)/fopc-ui-validation/fopc-ui-validation/lib/pattern/sampackag/validate_special_metrics.py:597: SettingWithCopyWarning: 
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  mpi_ros['unique_ronumber'] = mpi_ros['ronumber'].astype(str) + '_' + mpi_ros['closeddate'].astype(str)
Debug - calculate_mpi_penetration: MPI opportunities kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk       ronumber open_month    opendate  ...      realm   unique_ro_number group
0       987067    2023-08  2023-08-08  ...  sampackag  987067_2023-11-01     C
6       987067    2023-08  2023-08-08  ...  sampackag  987067_2023-11-01     C
7       987067    2023-08  2023-08-08  ...  sampackag  987067_2023-11-01     C
10      987067    2023-08  2023-08-08  ...  sampackag  987067_2023-11-01     C
11      987067    2023-08  2023-08-08  ...  sampackag  987067_2023-11-01     C
...        ...        ...         ...  ...        ...                ...   ...
17490   999134    2023-11  2023-11-30  ...  sampackag  999134_2023-11-30     C
17493   999134    2023-11  2023-11-30  ...  sampackag  999134_2023-11-30     C
17494   999134    2023-11  2023-11-30  ...  sampackag  999134_2023-11-30     C
17496   999136    2023-11  2023-11-30  ...  sampackag  999136_2023-11-30     C
17498   999125    2023-11  2023-11-30  ...  sampackag  999125_2023-11-30     C

[7525 rows x 50 columns]
Debug - calculate_mpi_penetration: Opportunities vvvvvvvvvvvvvvvvvvvvvvvvvvv 2599
Debug - calculate_mpi_penetration: MPI opcodes DB 1111111111111111111111----------- <db_handler.db_connector.MPIOpcodesTableResult object at 0x7fb22a46fec0>
Debug - calculate_mpi_penetration: MPI opcodes 222222222222222222222222---------- {'00095P', '99PX', '00099P', '99PM'}
Debug - calculate_mpi_penetration: MPI penetration 333333333333333333-------- 1.89
Debug - calculate_menu_penetration: Entering function       ronumber open_month    opendate  ...      realm   unique_ro_number group
0       987067    2023-08  2023-08-08  ...  sampackag  987067_2023-11-01     C
1       994077    2023-10  2023-10-12  ...  sampackag  994077_2023-11-01     C
2       995910    2023-10  2023-10-30  ...  sampackag  995910_2023-11-01     C
3       996045    2023-10  2023-10-31  ...  sampackag  996045_2023-11-01     W
4       996065    2023-10  2023-10-31  ...  sampackag  996065_2023-11-01     I
...        ...        ...         ...  ...        ...                ...   ...
17494   999134    2023-11  2023-11-30  ...  sampackag  999134_2023-11-30     C
17495   999136    2023-11  2023-11-30  ...  sampackag  999136_2023-11-30     C
17496   999136    2023-11  2023-11-30  ...  sampackag  999136_2023-11-30     C
17497   999136    2023-11  2023-11-30  ...  sampackag  999136_2023-11-30     C
17498   999125    2023-11  2023-11-30  ...  sampackag  999125_2023-11-30     C

[17499 rows x 50 columns]
Debug - calculate_menu_penetration: Menu opportunities ############################--0>       ronumber open_month    opendate  ...      realm   unique_ro_number group
0       987067    2023-08  2023-08-08  ...  sampackag  987067_2023-11-01     C
6       987067    2023-08  2023-08-08  ...  sampackag  987067_2023-11-01     C
7       987067    2023-08  2023-08-08  ...  sampackag  987067_2023-11-01     C
10      987067    2023-08  2023-08-08  ...  sampackag  987067_2023-11-01     C
11      987067    2023-08  2023-08-08  ...  sampackag  987067_2023-11-01     C
...        ...        ...         ...  ...        ...                ...   ...
17490   999134    2023-11  2023-11-30  ...  sampackag  999134_2023-11-30     C
17493   999134    2023-11  2023-11-30  ...  sampackag  999134_2023-11-30     C
17494   999134    2023-11  2023-11-30  ...  sampackag  999134_2023-11-30     C
17496   999136    2023-11  2023-11-30  ...  sampackag  999136_2023-11-30     C
17498   999125    2023-11  2023-11-30  ...  sampackag  999125_2023-11-30     C

[7525 rows x 50 columns]
Debug - calculate_menu_penetration: Menu master DB ############################--1> <db_handler.db_connector.menuMasterTableResult object at 0x7fb231038e60>
Debug - calculate_menu_penetration: Menu master DF ############################--2> Empty DataFrame
Columns: [menu_name, milegae_interval, before_miles, after_miles, max_miles, service_type_id, range_from, range_to, price, frh, items, store_id, user_name, lastmodifydate, is_default, realm]
Index: []
Debug - calculate_menu_penetration: Assigned menu opcodes DB ###########################---3> <db_handler.db_connector.assignedMenuOpcodesTableResult object at 0x7fb22a46d700>
Debug - calculate_menu_penetration: Assigned menu opcodes DF ###########################---4> Empty DataFrame
Columns: [menu_name, menu_opcode, service_type, store_id, realm]
Index: []
Debug - CP Return Rate: Calling database function with advisor: None
Error while connecting to PostgreSQL for CP Return Rate: column "month_year" does not exist
LINE 4:         ORDER BY month_year DESC;
                         ^

Debug - CP Return Rate: No data returned from database
Loaded 113541 records from total_details table

Special metrics data written successfully to chart_processing_results/special_metrics_calculated_value.json

Special Metrics Summary for November 2023:
  Total ROs: 3036
  One-Line ROs: 882
  Multi-Line ROs: 740
  Parts to Labor Ratio: 1.01
