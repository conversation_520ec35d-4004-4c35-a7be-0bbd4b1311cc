#!/usr/bin/env python3
"""
Debug script for CP Return Rate - check what's causing zero values
"""

def debug_cp_return_rate_issue():
    """Debug the CP Return Rate zero values issue"""
    
    print("=== CP Return Rate Debug Analysis ===\n")
    
    print("ISSUE: CP Return Rate is returning 0.0 values")
    print("Similar to the shop supplies issue, this suggests a data filtering problem.\n")
    
    print("POTENTIAL CAUSES:")
    print("1. **Customer Pay Filtering Issue**:")
    print("   - Using wrong column for customer pay identification")
    print("   - Database function uses: LEFT JOIN paytype_retail_flag_setting WHERE mapped_paytype = 'C'")
    print("   - Python code was using: combined_revenue_details['group'] == 'C'")
    print("   - Should use: combined_revenue_details['paytypegroup'].isin(customer_pay_types)")
    print()
    
    print("2. **Missing VIN Column**:")
    print("   - Return rate calculation requires VIN data to track vehicle returns")
    print("   - If VIN column is missing or empty, no return rates can be calculated")
    print()
    
    print("3. **Date Range Issues**:")
    print("   - Date filtering might be too restrictive")
    print("   - Data might not span the required 18-month period")
    print()
    
    print("4. **Data Processing Pipeline**:")
    print("   - combined_revenue_details might not have the right data structure")
    print("   - Missing paytype mapping or group assignments")
    print()
    
    print("FIXES IMPLEMENTED:")
    print("1. **Updated Customer Pay Filtering**:")
    print("   OLD: (combined_revenue_details['group'] == 'C')")
    print("   NEW: (combined_revenue_details['paytypegroup'].isin(customer_pay_types))")
    print()
    
    print("2. **Added Debug Information**:")
    print("   - Total records count")
    print("   - Records after each filter step")
    print("   - Available paytypegroups")
    print("   - VIN column existence check")
    print("   - Date ranges and VIN counts per period")
    print()
    
    print("3. **Enhanced Error Handling**:")
    print("   - Check for VIN column existence")
    print("   - Detailed logging of filtering steps")
    print("   - Period-by-period debugging")
    print()
    
    print("EXPECTED DEBUG OUTPUT:")
    print("When you run the special metrics calculation, you should see:")
    print("  Debug - CP Return Rate: Total records in combined_revenue_details: XXXX")
    print("  Debug - CP Return Rate: Records after department filter: XXXX")
    print("  Debug - CP Return Rate: Customer pay types used: {'C', 'E', 'M'}")
    print("  Debug - CP Return Rate: Available paytypegroups: ['C', 'W', 'I', 'E', 'M', 'F']")
    print("  Debug - CP Return Rate: Records with customer pay types: XXXX")
    print("  Debug - CP Return Rate: Records after all filters: XXXX")
    print("  Debug - CP Return Rate: Unique VINs in customer pay data: XXXX")
    print()
    
    print("TROUBLESHOOTING STEPS:")
    print("1. **Check the debug output** to see where the filtering fails")
    print("2. **Verify VIN data exists** in your combined_revenue_details")
    print("3. **Check paytypegroup values** match expected customer pay types")
    print("4. **Ensure date range** covers sufficient historical data")
    print()
    
    print("COMMON ISSUES AND SOLUTIONS:")
    print()
    print("**Issue: 'Records after all filters: 0'**")
    print("Solution: Check if paytypegroup column has the expected values (C, E, M)")
    print("         Verify opcategory is not all 'N/A'")
    print("         Check if revenue/cost fields have non-zero values")
    print()
    
    print("**Issue: 'VIN column not found'**")
    print("Solution: Ensure your data source includes VIN information")
    print("         VIN is essential for tracking vehicle return visits")
    print()
    
    print("**Issue: 'Unique VINs: 0'**")
    print("Solution: Check if VIN column has actual data (not null/empty)")
    print("         Verify VIN format and data quality")
    print()
    
    print("**Issue: 'VINs found but no returns'**")
    print("Solution: Check if date ranges are appropriate")
    print("         Verify data spans multiple months for return analysis")
    print("         Ensure some vehicles actually return for service")
    print()
    
    print("DATABASE FUNCTION COMPARISON:")
    print("The PostgreSQL function filters data as:")
    print("  WHERE trd.department = 'Service'")
    print("    AND trd.hide_ro = '0'")
    print("    AND prfs.mapped_paytype = 'C'  -- Customer pay via paytype mapping")
    print("    AND trd.opcategory <> 'N/A'")
    print("    AND NOT (all revenue/cost = 0)")
    print()
    
    print("Python equivalent should be:")
    print("  department == 'Service'")
    print("  hide_ro == '0'")
    print("  paytypegroup in customer_pay_types  -- Where customer_pay_types = {'C', 'E', 'M'}")
    print("  opcategory != 'N/A'")
    print("  NOT (all revenue/cost == 0)")
    print()
    
    print("NEXT STEPS:")
    print("1. Run the special metrics calculation")
    print("2. Check the debug output in the console")
    print("3. Identify which filter is causing the zero records")
    print("4. Adjust the filtering logic based on your actual data structure")
    print()
    
    print("✅ The debug information will help identify the exact cause of zero values!")

if __name__ == "__main__":
    debug_cp_return_rate_issue()
