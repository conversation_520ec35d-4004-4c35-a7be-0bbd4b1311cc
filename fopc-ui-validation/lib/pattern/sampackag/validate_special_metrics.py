import math
import sys
sys.path.append('../')
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import pandas as pd
import numpy as np
from decimal import Decimal, ROUND_HALF_UP
import json

# Import your existing database connectors
from db_handler.db_connector import (
    getCustomerPayTypeGroupsList, menuMasterTableResult, 
    menuServiceTypeTableResult, assignedMenuModelsTableResult, 
    assignedMenuOpcodesTableResult, MPISetupTableResult, 
    MPIOpcodesTableResult, allRevenueDetailsCPOverview,allRevenueDetailsTable,totalDetailsTable,totalDetailsTable
)

def round_off(n, decimals=0):
    """Round off numbers with proper decimal handling"""
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

def zero_sales_check(df, columns):
    """Check if all specified columns sum to zero"""
    total_sum = df[columns].sum().sum()
    return total_sum == 0

def get_month_date_range_from_target(target_date_str):
    """Get the start and end date for the target month"""
    # Modified to handle YYYY-MM format
    target_date = datetime.strptime(target_date_str, "%Y-%m")    
    month_start = target_date.replace(day=1)    
    month_end = month_start + relativedelta(months=1) - timedelta(days=1)    
    return month_start, month_end

def calculate_shop_supplies_details(combined_revenue_details, advisor=None):
    """
    Calculate shop supplies details matching the database function logic

    Args:
        combined_revenue_details: DataFrame with revenue details
        advisor: List of service advisors or None for all

    Returns:
        dict: Shop supplies totals by month for different pay types
    """
    try:
        # Import the database connector for total_details
        # from db_handler.db_connector import totalDetailsTable

        # Step 1: Filter base data similar to temp_data_all_revenue_details
        base_filter = (
            (combined_revenue_details['department'] == 'Service') &
            (combined_revenue_details['hide_ro'] == '0')
        )

        # Apply advisor filter
        if advisor is None or advisor == {'all'}:
            advisor_filter = True
        else:
            advisor_filter = combined_revenue_details['serviceadvisor'].isin(advisor)

        temp_data = combined_revenue_details[base_filter & advisor_filter].copy()

        if temp_data.empty:
            return initialize_empty_shop_supplies()
        
        # Step 2: Create temp_all_revenue_details equivalent
        # First part: opcategory != 'N/A' and has some revenue/cost
        valid_ops = temp_data[
            (temp_data['opcategory'] != 'N/A') &
            ~((temp_data['lbrsale'].fillna(0) == 0) & 
              (temp_data['lbrsoldhours'].fillna(0) == 0) & 
              (temp_data['prtextendedsale'].fillna(0) == 0) & 
              (temp_data['prtextendedcost'].fillna(0) == 0)) &
            (temp_data['paytypegroup'].isin(['C', 'E', 'M', 'W', 'F', 'I']))
        ].copy()
        
        # Map paytype using paytype_retail_flag_setting logic
        paytype_mapping = {'C': 'C', 'E': 'E', 'M': 'M', 'W': 'W', 'F': 'F', 'I': 'I'}
        valid_ops['mapped_paytype'] = valid_ops['paytypegroup'].map(paytype_mapping)
        
        # Second part: opcategory == 'N/A' or zero revenue/cost (mapped to 'I')
        invalid_ops = temp_data[
            (temp_data['opcategory'] == 'N/A') |
            ((temp_data['lbrsale'].fillna(0) == 0) & 
             (temp_data['lbrsoldhours'].fillna(0) == 0) & 
             (temp_data['prtextendedsale'].fillna(0) == 0) & 
             (temp_data['prtextendedcost'].fillna(0) == 0))
        ].copy()
        invalid_ops['mapped_paytype'] = 'I'
        
        # Combine both parts
        temp_all_revenue = pd.concat([valid_ops, invalid_ops], ignore_index=True)
        
        # Step 3: Determine RO_Type based on database logic
        temp_all_revenue['RO_Type'] = ''
        
        # Get ROs with Customer pay type
        customer_ros = temp_all_revenue[temp_all_revenue['mapped_paytype'] == 'C']['ronumber'].unique()
        temp_all_revenue.loc[temp_all_revenue['ronumber'].isin(customer_ros), 'RO_Type'] = 'C'
        
        # Get ROs with Warranty pay type (only if not already marked as Customer)
        warranty_ros = temp_all_revenue[
            (temp_all_revenue['mapped_paytype'] == 'W') & 
            (temp_all_revenue['RO_Type'] == '')
        ]['ronumber'].unique()
        temp_all_revenue.loc[temp_all_revenue['ronumber'].isin(warranty_ros), 'RO_Type'] = 'W'
        
        # Mark remaining as Internal
        temp_all_revenue.loc[temp_all_revenue['RO_Type'] == '', 'RO_Type'] = 'I'
        
        # Step 4: Get unique ROs with their types
        unique_ros = temp_all_revenue[['ronumber', 'closeddate', 'RO_Type', 'store_id']].drop_duplicates()
        
        # Step 5: Get shop supplies data from total_details table
        try:
            total_details_db_connect = totalDetailsTable()
            total_details_df = total_details_db_connect.getTableResult()
            print(f"Loaded {len(total_details_df)} records from total_details table")
        except Exception as e:
            print(f"Error loading total_details: {str(e)}")
            print("Warning: totalshopsupply data not available. Using placeholder values.")
            total_details_df = pd.DataFrame(columns=['ronumber', 'closeddate', 'store_id', 'totalshopsupply'])

        # Join with shop supplies data
        if not total_details_df.empty:
            # Convert data types for proper joining
            total_details_df['ronumber'] = total_details_df['ronumber'].astype(str)
            total_details_df['store_id'] = total_details_df['store_id'].astype(str)
            total_details_df['closeddate'] = pd.to_datetime(total_details_df['closeddate'])

            # Ensure unique_ros has proper data types
            unique_ros['ronumber'] = unique_ros['ronumber'].astype(str)
            unique_ros['store_id'] = unique_ros['store_id'].astype(str)
            unique_ros['closeddate'] = pd.to_datetime(unique_ros['closeddate'])

            shop_supplies_data = pd.merge(
                total_details_df[['ronumber', 'closeddate', 'store_id', 'totalshopsupply']],
                unique_ros,
                on=['ronumber', 'closeddate', 'store_id'],
                how='inner'
            )
        else:
            print("Warning: No shop supplies data available. Using placeholder values.")
            shop_supplies_data = unique_ros.copy()
            shop_supplies_data['totalshopsupply'] = 0
        
        if shop_supplies_data.empty:
            return initialize_empty_shop_supplies()
        
        # Step 6: Convert closeddate to datetime and create month_year
        shop_supplies_data['closeddate'] = pd.to_datetime(shop_supplies_data['closeddate'])
        shop_supplies_data['month_year'] = shop_supplies_data['closeddate'].dt.strftime('%Y-%m')
        shop_supplies_data['month_first_day'] = shop_supplies_data['closeddate'].dt.to_period('M').dt.start_time
        
        # Step 7: Group by month and RO_Type, then sum shop supplies
        monthly_grouped = shop_supplies_data.groupby(['month_first_day', 'RO_Type'])['totalshopsupply'].sum().reset_index()
        
        if monthly_grouped.empty:
            return initialize_empty_shop_supplies()
        
        # Step 8: Pivot to get separate columns for each RO_Type
        monthly_pivot = monthly_grouped.pivot(index='month_first_day', columns='RO_Type', values='totalshopsupply').fillna(0)
        
        # Ensure all required columns exist
        for col in ['C', 'W', 'I']:
            if col not in monthly_pivot.columns:
                monthly_pivot[col] = 0
        
        # Calculate combined totals
        monthly_pivot['Combined'] = monthly_pivot['C'] + monthly_pivot['W'] + monthly_pivot['I']
        
        # Sort by date descending (most recent first)
        monthly_pivot = monthly_pivot.sort_index(ascending=False)
        
        # Return the most recent month's data
        if not monthly_pivot.empty:
            latest_month = monthly_pivot.iloc[0]
            return {
                "combined": round_off(latest_month['Combined'], 2),
                "customer_pay": round_off(latest_month['C'], 2),
                "warranty": round_off(latest_month['W'], 2),
                "internal": round_off(latest_month['I'], 2),
                "month": monthly_pivot.index[0].strftime('%Y-%m')
            }
        
        return initialize_empty_shop_supplies()
        
    except Exception as e:
        print(f"Error calculating shop supplies details: {str(e)}")
        return initialize_empty_shop_supplies()

def initialize_empty_shop_supplies():
    """Initialize empty shop supplies structure"""
    return {
        "combined": 0.00,
        "customer_pay": 0.00,
        "warranty": 0.00,
        "internal": 0.00,
        "month": "No Data"
    }

def process_target_month_special_metrics(all_revenue_details_df, month_start, month_end, advisor, tech, 
                                       retail_flag, customer_pay_types, warranty_pay_types, columns_to_check):
    """Process special metrics data for the target month and return results"""
    
    month_start = month_start.date()
    month_end = month_end.date()   
    
    # Filter data for the specific target month
    month_data = all_revenue_details_df[
        (all_revenue_details_df['closeddate'] >= month_start) &
        (all_revenue_details_df['closeddate'] <= month_end)
    ]    
    
    if month_data.empty:
        return None
    
    # Apply existing filtering logic
    filtered_df = month_data[
        (month_data['department'] == 'Service') & 
        (month_data['hide_ro'] != True)
    ]    
    
    if filtered_df.empty:
        return None
    
    filtered_df = filtered_df.copy()
    filtered_df['unique_ro_number'] = filtered_df['ronumber'].astype(str) + '_' + filtered_df['closeddate'].astype(str)
    
    # Initialize the combined revenue details with group assignment
    combined_revenue_details = filtered_df.copy()
    combined_revenue_details['group'] = pd.Series(dtype="string")
    
    # Group assignment logic (same as original)
    temp_revenue_details = combined_revenue_details.copy()
    temp_revenue_details.loc[temp_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0

    for ro_number in combined_revenue_details['unique_ro_number'].unique():
        ro_specific_rows = temp_revenue_details[temp_revenue_details['unique_ro_number'] == ro_number]        
        ro_specific_rows_C = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(customer_pay_types)]
        ro_specific_rows_W = ro_specific_rows[ro_specific_rows['paytypegroup'].isin(warranty_pay_types)]       
        zero_sales_C = zero_sales_check(ro_specific_rows_C, columns_to_check)
        zero_sales_W = zero_sales_check(ro_specific_rows_W, columns_to_check)        
        
        if not ro_specific_rows_C.empty and not zero_sales_C:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'C'
        elif not ro_specific_rows_W.empty and not zero_sales_W:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'W'
        else:
            combined_revenue_details.loc[combined_revenue_details['unique_ro_number'] == ro_number, 'group'] = 'I'
    
    # Apply filters based on advisor and tech conditions
    if advisor == {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details['unique_ro_number'].unique()
    elif advisor != {'all'} and tech == {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['serviceadvisor'].astype(str).isin(advisor), 'unique_ro_number'].unique()
    elif advisor == {'all'} and tech != {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[combined_revenue_details['lbrtechno'].astype(str).isin(tech), 'unique_ro_number'].unique()
    elif advisor != {'all'} and tech != {'all'}:
        matching_ro_numbers = combined_revenue_details.loc[(combined_revenue_details['serviceadvisor'].astype(str).isin(advisor)) & 
            (combined_revenue_details['lbrtechno'].astype(str).isin(tech)), 'unique_ro_number'].unique()
    
    # Apply the advisor and tech filter conditions
    combined_revenue_details = combined_revenue_details[combined_revenue_details['unique_ro_number'].isin(matching_ro_numbers)]
    combined_revenue_details = combined_revenue_details.reset_index(drop=True)
    combined_revenue_details.loc[combined_revenue_details['opcategory'] == 'N/A', columns_to_check] = 0
    
    # Calculate RO counts
    Scorecard_10_CP = combined_revenue_details.loc[combined_revenue_details['group'] == 'C', 'unique_ro_number'].nunique()
    Scorecard_10_Wty = combined_revenue_details.loc[combined_revenue_details['group'] == 'W', 'unique_ro_number'].nunique()
    Scorecard_10_Int = combined_revenue_details.loc[combined_revenue_details['group'] == 'I', 'unique_ro_number'].nunique()
    
    # Initialize default values
    special_metrics_results = {
        "one_line_metrics": {
            "under_60k": 0,
            "over_60k": 0,
            "total_shop": 0,
            "perc_under_60k": 0,
            "perc_over_60k": 0,
            "perc_total_shop": 0
        },
        "multi_line_metrics": {
            "under_60k": 0,
            "over_60k": 0,
            "total_shop": 0,
            "perc_under_60k": 0,
            "perc_over_60k": 0,
            "perc_total_shop": 0
        },
        "average_open_days": {
            "customer_pay": 0,
            "warranty": 0,
            "internal": 0,
            "extended_service": 0,
            "maintenance": 0,
            "factory_service": 0
        },
        "parts_to_labor_ratio": {
            "overall": 0,
            "competitive": 0,
            "maintenance": 0,
            "repair": 0
        },
        "labor_hours_percentage": {
            "customer_pay": 0,
            "warranty": 0,
            "internal": 0,
            "extended_service": 0,
            "maintenance": 0,
            "factory_service": 0
        },
        "mpi_penetration_percentage": 0,
        "menu_penetration_percentage": 0,
        "shop_supplies": initialize_empty_shop_supplies()
    }
    
    # Only proceed with calculations if we have customer pay data
    if Scorecard_10_CP > 0:
        special_metrics_results = calculate_special_metrics(
            combined_revenue_details, customer_pay_types, warranty_pay_types, 
            columns_to_check, Scorecard_10_CP
        )
    
    # Calculate shop supplies regardless of customer pay data
    shop_supplies_results = calculate_shop_supplies_details(combined_revenue_details, advisor)
    special_metrics_results["shop_supplies"] = shop_supplies_results
    
    return {
        "target_month": month_start.strftime('%Y-%m'),
        "target_month_name": month_start.strftime('%B %Y'),
        "total_ros": Scorecard_10_CP + Scorecard_10_Wty + Scorecard_10_Int,
        "ro_counts": {
            "customer_pay_ros": Scorecard_10_CP,
            "warranty_ros": Scorecard_10_Wty,
            "internal_ros": Scorecard_10_Int
        },
        "special_metrics": special_metrics_results
    }

def calculate_special_metrics(combined_revenue_details, customer_pay_types, warranty_pay_types, columns_to_check, total_cp_ros):
    """Calculate all special metrics"""
    
    # Filter for customer pay data with mileage information
    all_revenue_details_list = combined_revenue_details.to_dict('records')
    
    total_revenue_details_C = [
        row for row in all_revenue_details_list
        if (row['group'] == 'C') and (row['paytypegroup'] in customer_pay_types) and 
        (row['mileage'] is not None)
    ]
    
    if not total_revenue_details_C:
        return initialize_empty_metrics()
    
    total_revenue_details_C_df = pd.DataFrame(total_revenue_details_C)
    
    # Remove zero-sales rows
    total_revenue_details_C_df = total_revenue_details_C_df[
        ~((total_revenue_details_C_df['lbrsale'].fillna(0) == 0) &
          (total_revenue_details_C_df['lbrsoldhours'].fillna(0) == 0) &
          (total_revenue_details_C_df['prtextendedsale'].fillna(0) == 0) &
          (total_revenue_details_C_df['prtextendedcost'].fillna(0) == 0))
    ]
    
    # Convert numeric columns
    numeric_cols = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
    for col in numeric_cols:
        total_revenue_details_C_df[col] = pd.to_numeric(total_revenue_details_C_df[col], errors='coerce').fillna(0)
    
    # Calculate one-line and multi-line metrics
    one_line_metrics, multi_line_metrics = calculate_line_ro_metrics(total_revenue_details_C_df, total_cp_ros)
    
    # Calculate average open days
    avg_open_days = calculate_average_open_days(combined_revenue_details)
    
    # Calculate parts to labor ratio
    parts_to_labor = calculate_parts_to_labor_ratio(total_revenue_details_C_df)
    
    # Calculate labor hours percentage
    labor_hours_perc = calculate_labor_hours_percentage(combined_revenue_details)
    
    # Calculate MPI and Menu penetration (simplified versions)
    mpi_penetration = calculate_mpi_penetration(combined_revenue_details)
    menu_penetration = calculate_menu_penetration(combined_revenue_details)

    # Calculate CP Return Rate
    cp_return_rate = get_cp_return_rate_summary(combined_revenue_details, customer_pay_types=customer_pay_types)

    return {
        "one_line_metrics": one_line_metrics,
        "multi_line_metrics": multi_line_metrics,
        "average_open_days": avg_open_days,
        "parts_to_labor_ratio": parts_to_labor,
        "labor_hours_percentage": labor_hours_perc,
        "mpi_penetration_percentage": mpi_penetration,
        "menu_penetration_percentage": menu_penetration,
        "shop_supplies": initialize_empty_shop_supplies(),  # Will be calculated separately
        "cp_return_rate": cp_return_rate
    }


def calculate_line_ro_metrics(df, total_cp_ros):
    """Calculate one-line and multi-line RO metrics"""
    
    # Split by mileage
    below_60k = df[df['mileage'].astype(int) < 60000]
    above_60k = df[df['mileage'].astype(int) >= 60000]
    
    total_ro_count_below_60k = below_60k['unique_ro_number'].nunique()
    total_ro_count_above_60k = above_60k['unique_ro_number'].nunique()
    
    # Identify one-line vs multi-line ROs
    value_counts = df['unique_ro_number'].value_counts()
    one_line_ROs = value_counts[value_counts == 1].index
    
    One_Line_RO_Details = df[df['unique_ro_number'].isin(one_line_ROs)]
    Multi_Line_RO_Details = df[~df['unique_ro_number'].isin(one_line_ROs)]
    
    # Split one-line and multi-line by mileage
    one_line_below60k = One_Line_RO_Details[One_Line_RO_Details['mileage'].astype(int) < 60000]
    one_line_above60k = One_Line_RO_Details[One_Line_RO_Details['mileage'].astype(int) >= 60000]
    multi_line_below60k = Multi_Line_RO_Details[Multi_Line_RO_Details['mileage'].astype(int) < 60000]
    multi_line_above60k = Multi_Line_RO_Details[Multi_Line_RO_Details['mileage'].astype(int) >= 60000]
    
    # Calculate counts
    one_line_count_below_60k = one_line_below60k.shape[0]
    one_line_count_above_60k = one_line_above60k.shape[0]
    multi_line_count_below_60k = multi_line_below60k['unique_ro_number'].nunique()
    multi_line_count_above_60k = multi_line_above60k['unique_ro_number'].nunique()
    
    one_line_total = one_line_count_below_60k + one_line_count_above_60k
    multi_line_total = multi_line_count_below_60k + multi_line_count_above_60k
    
    # Calculate percentages
    one_line_perc_below_60k = round_off((one_line_count_below_60k / total_ro_count_below_60k) * 100, 2) if total_ro_count_below_60k > 0 else 0
    one_line_perc_above_60k = round_off((one_line_count_above_60k / total_ro_count_above_60k) * 100, 2) if total_ro_count_above_60k > 0 else 0
    one_line_perc_total = round_off((one_line_total / total_cp_ros) * 100, 2) if total_cp_ros > 0 else 0
    
    multi_line_perc_below_60k = round_off((multi_line_count_below_60k / total_ro_count_below_60k) * 100, 2) if total_ro_count_below_60k > 0 else 0
    multi_line_perc_above_60k = round_off((multi_line_count_above_60k / total_ro_count_above_60k) * 100, 2) if total_ro_count_above_60k > 0 else 0
    multi_line_perc_total = round_off((multi_line_total / total_cp_ros) * 100, 2) if total_cp_ros > 0 else 0
    
    one_line_metrics = {
        "under_60k": one_line_count_below_60k,
        "over_60k": one_line_count_above_60k,
        "total_shop": one_line_total,
        "perc_under_60k": one_line_perc_below_60k,
        "perc_over_60k": one_line_perc_above_60k,
        "perc_total_shop": one_line_perc_total
    }
    
    multi_line_metrics = {
        "under_60k": multi_line_count_below_60k,
        "over_60k": multi_line_count_above_60k,
        "total_shop": multi_line_total,
        "perc_under_60k": multi_line_perc_below_60k,
        "perc_over_60k": multi_line_perc_above_60k,
        "perc_total_shop": multi_line_perc_total
    }
    
    return one_line_metrics, multi_line_metrics

def calculate_average_open_days(combined_revenue_details):
    """Calculate average open days by pay type"""
    
    # Filter out zero-sales rows
    filtered_df_with_sales = combined_revenue_details[
        ~((pd.to_numeric(combined_revenue_details['lbrsale'], errors='coerce').fillna(0) == 0) &
          (pd.to_numeric(combined_revenue_details['lbrsoldhours'], errors='coerce').fillna(0) == 0) &
          (pd.to_numeric(combined_revenue_details['prtextendedsale'], errors='coerce').fillna(0) == 0) &
          (pd.to_numeric(combined_revenue_details['prtextendedcost'], errors='coerce').fillna(0) == 0))
    ]
    
    # Calculate open days
    filtered_df_with_sales['min_opendate'] = filtered_df_with_sales.groupby('unique_ro_number')['opendate'].transform('min')
    filtered_df_with_sales['open_days'] = (pd.to_datetime(filtered_df_with_sales['closeddate']) - pd.to_datetime(filtered_df_with_sales['min_opendate'])).dt.days
    
    # Group by pay types
    pay_type_groups = {
        'C': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'C'],
        'W': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'W'],
        'I': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'I'],
        'E': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'E'],
        'M': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'M'],
        'F': filtered_df_with_sales[filtered_df_with_sales['paytypegroup'] == 'F']
    }
    
    avg_days = {}
    for pay_type, group_df in pay_type_groups.items():
        if not group_df.empty:
            # Get unique ROs with minimum open days
            unique_ros = group_df.loc[group_df.groupby('unique_ro_number')['open_days'].idxmin()]
            ro_count = len(set(group_df['unique_ro_number']))
            avg_days[pay_type] = round_off(unique_ros['open_days'].sum() / ro_count, 2) if ro_count > 0 else 0
        else:
            avg_days[pay_type] = 0
    
    return {
        "customer_pay": avg_days.get('C', 0),
        "warranty": avg_days.get('W', 0),
        "internal": avg_days.get('I', 0),
        "extended_service": avg_days.get('E', 0),
        "maintenance": avg_days.get('M', 0),
        "factory_service": avg_days.get('F', 0)
    }

def calculate_parts_to_labor_ratio(df):
    """Calculate parts to labor ratio overall and by category"""
    
    # Overall ratio
    total_labor_sale = df['lbrsale'].sum()
    total_parts_sale = df['prtextendedsale'].sum()
    overall_ratio = round_off(total_parts_sale / total_labor_sale, 2) if total_labor_sale > 0 else 0
    
    # By category
    categories = {
        'competitive': df[df['opcategory'] == 'COMPETITIVE'],
        'maintenance': df[df['opcategory'] == 'MAINTENANCE'],
        'repair': df[df['opcategory'] == 'REPAIR']
    }
    
    category_ratios = {}
    for category, cat_df in categories.items():
        if not cat_df.empty:
            cat_labor = cat_df['lbrsale'].sum()
            cat_parts = cat_df['prtextendedsale'].sum()
            category_ratios[category] = round_off(cat_parts / cat_labor, 2) if cat_labor > 0 else 0
        else:
            category_ratios[category] = 0
    
    return {
        "overall": overall_ratio,
        "competitive": category_ratios.get('competitive', 0),
        "maintenance": category_ratios.get('maintenance', 0),
        "repair": category_ratios.get('repair', 0)
    }

def calculate_labor_hours_percentage(combined_revenue_details):
    """Calculate labor hours percentage by pay type"""
    
    total_sold_hours = pd.to_numeric(combined_revenue_details['lbrsoldhours'], errors='coerce').fillna(0).sum()
    
    pay_types = ['C', 'W', 'I', 'E', 'M', 'F']
    pay_type_names = ['customer_pay', 'warranty', 'internal', 'extended_service', 'maintenance', 'factory_service']
    
    percentages = {}
    for pay_type, name in zip(pay_types, pay_type_names):
        pay_type_df = combined_revenue_details[combined_revenue_details['paytypegroup'] == pay_type]
        if not pay_type_df.empty:
            pay_type_hours = pd.to_numeric(pay_type_df['lbrsoldhours'], errors='coerce').fillna(0).sum()
            percentages[name] = round_off((pay_type_hours / total_sold_hours) * 100, 2) if total_sold_hours > 0 else 0
        else:
            percentages[name] = 0
    
    return percentages

def calculate_mpi_penetration(combined_revenue_details):
    """Calculate MPI penetration percentage"""
    try:
        # Filter for valid opportunities
        mpi_opportunities = combined_revenue_details[
            (combined_revenue_details['group'].isin(['C', 'W'])) & 
            (combined_revenue_details['paytypegroup'].isin(['C', 'M', 'E', 'W', 'F'])) &
            (combined_revenue_details['department'] != 'Body Shop') &
            (combined_revenue_details['hide_ro'] != True)
        ]
        print("Debug - calculate_mpi_penetration: MPI opportunities kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk",mpi_opportunities)
        # Get unique RO count for opportunities
        mpi_opportunities['unique_ronumber'] = mpi_opportunities['ronumber'].astype(str) + '_' + mpi_opportunities['closeddate'].astype(str)
        opportunities = mpi_opportunities['unique_ronumber'].nunique()
        print("Debug - calculate_mpi_penetration: Opportunities vvvvvvvvvvvvvvvvvvvvvvvvvvv",opportunities)
        if opportunities == 0:
            return 0

        # Get MPI opcodes
        mpi_opcodes_db = MPIOpcodesTableResult()
        mpi_opcodes = mpi_opcodes_db.getTableResult()
        print("Debug - calculate_mpi_penetration: MPI opcodes DB 1111111111111111111111-----------",mpi_opcodes_db)
        print("Debug - calculate_mpi_penetration: MPI opcodes 222222222222222222222222----------",mpi_opcodes)

        # Filter ROs with MPI jobs
        mpi_ros = combined_revenue_details[
            (combined_revenue_details['lbropcode'].isin(mpi_opcodes)) &
            (combined_revenue_details['group'].isin(['C', 'W'])) &
            (combined_revenue_details['paytypegroup'].isin(['C', 'M', 'E', 'W', 'F'])) &
            (combined_revenue_details['department'] != 'Body Shop') &
            (combined_revenue_details['hide_ro'] != True)
        ]

        # Get unique completed MPI RO count
        mpi_ros['unique_ronumber'] = mpi_ros['ronumber'].astype(str) + '_' + mpi_ros['closeddate'].astype(str)
        completed_mpi_count = mpi_ros['unique_ronumber'].nunique()
        # Calculate percentage
        mpi_penetration = round((completed_mpi_count / opportunities) * 100, 2)
        print("Debug - calculate_mpi_penetration: MPI penetration 333333333333333333--------",mpi_penetration)
        return mpi_penetration

    except Exception as e:
        print(f"Error calculating MPI penetration: {str(e)}")
        return 0

def calculate_menu_penetration(combined_revenue_details):
    """Calculate menu penetration percentage"""
    print("Debug - calculate_menu_penetration: Entering function",combined_revenue_details)
    try:
        # Filter for valid opportunities
        menu_opportunities = combined_revenue_details[
            (combined_revenue_details['group'].isin(['C', 'W'])) &
            (combined_revenue_details['paytypegroup'].isin(['C', 'M', 'E', 'W', 'F'])) &
            (combined_revenue_details['department'] != 'Body Shop')
        ]
        # print("Debug - calculate_menu_penetration: Menu opportunities",menu_opportunities)
        if menu_opportunities.empty:
            return 0
        print("Debug - calculate_menu_penetration: Menu opportunities ############################--0>",menu_opportunities)
        # Get menu setup data
        menu_master_db = menuMasterTableResult()
        menu_master_df = menu_master_db.getTableResult()
        
        print("Debug - calculate_menu_penetration: Menu master DB ############################--1>",menu_master_db)
        print("Debug - calculate_menu_penetration: Menu master DF ############################--2>",menu_master_df)
        
        assigned_menu_opcodes_db = assignedMenuOpcodesTableResult()
        assigned_menu_opcodes_df = assigned_menu_opcodes_db.getTableResult()
        
        print("Debug - calculate_menu_penetration: Assigned menu opcodes DB ###########################---3>",assigned_menu_opcodes_db)
        print("Debug - calculate_menu_penetration: Assigned menu opcodes DF ###########################---4>",assigned_menu_opcodes_df)
        
        if menu_master_df.empty or assigned_menu_opcodes_df.empty:
            return 0

        # Get unique opportunity count
        menu_opportunities['unique_ronumber'] = menu_opportunities['ronumber'].astype(str) + '_' + menu_opportunities['closeddate'].astype(str)
        opportunities = menu_opportunities['unique_ronumber'].nunique()

        print("Debug - calculate_menu_penetration: Opportunities ###############################---5>",opportunities)
        if opportunities == 0:
            return 0

        # Get all menu opcodes
        menu_opcodes = set(assigned_menu_opcodes_df['menu_opcode'].str.strip().str.upper())
        print("Debug - calculate_menu_penetration: Menu opcodes ###############################---6>",menu_opcodes)
        # Filter ROs with menu opcodes and customer pay conditions
        menu_ros = menu_opportunities[
            (menu_opportunities['lbropcode'].str.strip().str.upper().isin(menu_opcodes)) &
            (menu_opportunities['group'] == 'C') &
            (menu_opportunities['paytypegroup'].isin(['C', 'M', 'E']))
        ]
        print("Debug - calculate_menu_penetration: Menu ROS ###############################---7>",menu_ros)

        # Get unique menu RO count
        menu_sold = menu_ros['unique_ronumber'].nunique()

        print("Debug - calculate_menu_penetration: Menu sold ###############################---8>",menu_sold)

        # Calculate percentage
        menu_penetration = round((menu_sold / opportunities) * 100, 2)
        print("Debug - calculate_menu_penetration: Menu penetration ###############################---9>",menu_penetration)
        # print(f"Debug - Menu Penetration: Opportunities={opportunities}, Menu Sold={menu_sold}, Percentage={menu_penetration}")
        return menu_penetration

    except Exception as e:
        print(f"Error calculating menu penetration: {str(e)}")
        print("Debug - Menu Data:")
        print(f"Menu Master Records: {len(menu_master_df) if not menu_master_df.empty else 0}")
        print(f"Menu Opcodes Records: {len(assigned_menu_opcodes_df) if not assigned_menu_opcodes_df.empty else 0}")
        return 0


def get_total_shopsupplies_details_combined(combined_revenue_details, advisor=None):
    """
    Calculate total shop supplies details combined matching the database function logic
    
    Args:
        combined_revenue_details: DataFrame with revenue details
        advisor: List of service advisors or None for all
    
    Returns:
        dict: JSON data structure with shop supplies totals by month for different pay types
    """
    try:
        # import pandas as pd
        
        # Step 1: Filter base data similar to temp_data_all_revenue_details
        base_filter = (
            (combined_revenue_details['department'] == 'Service') &
            (combined_revenue_details['hide_ro'] == '0')
        )
        
        # Apply advisor filter
        if advisor is None or advisor == ['All']:
            advisor_filter = True
        else:
            advisor_filter = combined_revenue_details['serviceadvisor'].isin(advisor)
        
        temp_data = combined_revenue_details[base_filter & advisor_filter].copy()
        
        # Step 2: Create temp_all_revenue_details equivalent
        # First part: opcategory != 'N/A' and has some revenue/cost
        valid_ops = temp_data[
            (temp_data['opcategory'] != 'N/A') &
            ~((temp_data['lbrsale'] == 0) & 
              (temp_data['lbrsoldhours'] == 0) & 
              (temp_data['prtextendedsale'] == 0) & 
              (temp_data['prtextendedcost'] == 0)) &
            (temp_data['paytypegroup'].isin(['C', 'E', 'M', 'W', 'F', 'I']))
        ].copy()
        
        # Map paytype using paytype_retail_flag_setting logic
        valid_ops['mapped_paytype'] = valid_ops['paytypegroup'].map({
            'C': 'C', 'E': 'E', 'M': 'M', 'W': 'W', 'F': 'F', 'I': 'I'
        })
        
        # Second part: opcategory == 'N/A' or zero revenue/cost (mapped to 'I')
        invalid_ops = temp_data[
            (temp_data['opcategory'] == 'N/A') |
            ((temp_data['lbrsale'].fillna(0) == 0) & 
             (temp_data['lbrsoldhours'].fillna(0) == 0) & 
             (temp_data['prtextendedsale'].fillna(0) == 0) & 
             (temp_data['prtextendedcost'].fillna(0) == 0))
        ].copy()
        invalid_ops['mapped_paytype'] = 'I'
        
        # Combine both parts
        temp_all_revenue = pd.concat([valid_ops, invalid_ops], ignore_index=True)
        
        # Step 3: Determine RO_Type based on database logic
        temp_all_revenue['RO_Type'] = ''
        
        # Get ROs with Customer pay type
        customer_ros = temp_all_revenue[temp_all_revenue['mapped_paytype'] == 'C']['ronumber'].unique()
        temp_all_revenue.loc[temp_all_revenue['ronumber'].isin(customer_ros), 'RO_Type'] = 'C'
        
        # Get ROs with Warranty pay type (only if not already marked as Customer)
        warranty_ros = temp_all_revenue[
            (temp_all_revenue['mapped_paytype'] == 'W') & 
            (temp_all_revenue['RO_Type'] == '')
        ]['ronumber'].unique()
        temp_all_revenue.loc[temp_all_revenue['ronumber'].isin(warranty_ros), 'RO_Type'] = 'W'
        
        # Mark remaining as Internal
        temp_all_revenue.loc[temp_all_revenue['RO_Type'] == '', 'RO_Type'] = 'I'
        
        # Step 4: Get unique ROs with their types
        unique_ros = temp_all_revenue[['ronumber', 'closeddate', 'RO_Type', 'store_id']].drop_duplicates()
        
        # Step 5: Get shop supplies data from total_details table
        try:
            # from db_handler.db_connector import totalDetailsTable
            total_details_db_connect = totalDetailsTable()
            total_details_df = total_details_db_connect.getTableResult()
            print(f"Loaded {len(total_details_df)} records from total_details table")
        except Exception as e:
            print(f"Error loading total_details: {str(e)}")
            print("Warning: totalshopsupply data not available. Using placeholder values.")
            total_details_df = pd.DataFrame(columns=['ronumber', 'closeddate', 'store_id', 'totalshopsupply'])

        # Join with shop supplies data
        if not total_details_df.empty:
            # Convert data types for proper joining
            total_details_df['ronumber'] = total_details_df['ronumber'].astype(str)
            total_details_df['store_id'] = total_details_df['store_id'].astype(str)
            total_details_df['closeddate'] = pd.to_datetime(total_details_df['closeddate'])

            # Ensure unique_ros has proper data types
            unique_ros['ronumber'] = unique_ros['ronumber'].astype(str)
            unique_ros['store_id'] = unique_ros['store_id'].astype(str)
            unique_ros['closeddate'] = pd.to_datetime(unique_ros['closeddate'])

            shop_supplies_data = pd.merge(
                total_details_df[['ronumber', 'closeddate', 'store_id', 'totalshopsupply']],
                unique_ros,
                on=['ronumber', 'closeddate', 'store_id'],
                how='inner'
            )
        else:
            print("Warning: No shop supplies data available. Using placeholder values.")
            shop_supplies_data = unique_ros.copy()
            shop_supplies_data['totalshopsupply'] = 0
        
        # Step 6: Convert closeddate to datetime and create month_year
        shop_supplies_data['closeddate'] = pd.to_datetime(shop_supplies_data['closeddate'])
        shop_supplies_data['month_year'] = shop_supplies_data['closeddate'].dt.strftime('%Y-%m')
        shop_supplies_data['month_first_day'] = shop_supplies_data['closeddate'].dt.to_period('M').dt.start_time
        
        # Step 7: Group by month and RO_Type, then sum shop supplies
        monthly_grouped = shop_supplies_data.groupby(['month_first_day', 'RO_Type'])['totalshopsupply'].sum().reset_index()
        
        # Step 8: Pivot to get separate columns for each RO_Type
        monthly_pivot = monthly_grouped.pivot(index='month_first_day', columns='RO_Type', values='totalshopsupply').fillna(0)
        
        # Ensure all required columns exist
        for col in ['C', 'W', 'I']:
            if col not in monthly_pivot.columns:
                monthly_pivot[col] = 0
        
        # Calculate combined totals
        monthly_pivot['Combined'] = monthly_pivot['C'] + monthly_pivot['W'] + monthly_pivot['I']
        
        # Sort by date descending (most recent first)
        monthly_pivot = monthly_pivot.sort_index(ascending=False)
        
        # Step 9: Create JSON structure matching database output
        json_data = [{
            'datasets': [
                {
                    'data': monthly_pivot['Combined'].astype(str).tolist(),
                    'label': 'Combined',
                    'chartId': '1359'
                },
                {
                    'data': monthly_pivot['C'].astype(str).tolist(),
                    'label': 'Customer Pay',
                    'chartId': '1360'
                },
                {
                    'data': monthly_pivot['W'].astype(str).tolist(),
                    'label': 'Warranty',
                    'chartId': '1361'
                },
                {
                    'data': monthly_pivot['I'].astype(str).tolist(),
                    'label': 'Internal',
                    'chartId': '1362'
                }
            ],
            'labels': monthly_pivot.index.strftime('%Y-%m-%d').tolist()
        }]
        
        return {'json_data': json_data}
        
    except Exception as e:
        print(f"Error calculating shop supplies details: {str(e)}")
        return {
            'json_data': [{
                'datasets': [
                    {
                        'data': ['0.00'],
                        'label': 'Combined',
                        'chartId': '1359'
                    },
                    {
                        'data': ['0.00'],
                        'label': 'Customer Pay',
                        'chartId': '1360'
                    },
                    {
                        'data': ['0.00'],
                        'label': 'Warranty',
                        'chartId': '1361'
                    },
                    {
                        'data': ['0.00'],
                        'label': 'Internal',
                        'chartId': '1362'
                    }
                ],
                'labels': ['No Data']
            }]
        }


def get_shop_supplies_summary(combined_revenue_details, advisor=None, target_month=None):
    """
    Get shop supplies summary for a specific month or latest month
    
    Args:
        combined_revenue_details: DataFrame with revenue details
        advisor: List of service advisors or None for all
        target_month: Specific month in 'YYYY-MM' format, or None for latest
    
    Returns:
        dict: Shop supplies totals by pay type
    """
    try:
        # Get the full monthly data
        monthly_data = get_total_shopsupplies_details_combined(combined_revenue_details, advisor)
        
        # Extract the data
        datasets = monthly_data['json_data'][0]['datasets']
        labels = monthly_data['json_data'][0]['labels']
        
        if not labels or labels[0] == 'No Data':
            return {
                'combined': 0.00,
                'customer_pay': 0.00,
                'warranty': 0.00,
                'internal': 0.00,
                'month': 'No Data'
            }
        
        # Find target month index (default to most recent - index 0)
        index = 0
        if target_month:
            target_date = f"{target_month}-01"
            if target_date in labels:
                index = labels.index(target_date)
        
        return {
            'combined': float(datasets[0]['data'][index]),
            'customer_pay': float(datasets[1]['data'][index]),
            'warranty': float(datasets[2]['data'][index]),
            'internal': float(datasets[3]['data'][index]),
            'month': labels[index]
        }
        
    except Exception as e:
        print(f"Error getting shop supplies summary: {str(e)}")
        return {
            'combined': 0.00,
            'customer_pay': 0.00,
            'warranty': 0.00,
            'internal': 0.00,
            'month': 'Error'
        }

def calculate_cp_return_rate(combined_revenue_details=None, advisor=None, customer_pay_types=None):
    """
    Calculate Customer Pay Return Rate using the database function

    Args:
        combined_revenue_details: DataFrame with revenue details (not used, kept for compatibility)
        advisor: List of service advisors or None for all
        customer_pay_types: Set of customer pay types (not used, kept for compatibility)

    Returns:
        dict: JSON data structure with 6-month and 12-month return rates by month
    """
    try:
        # Import the database connector
        from db_handler.db_connector import cpReturnRateTable

        print(f"Debug - CP Return Rate: Calling database function with advisor: {advisor}")

        # Call the database function
        cp_return_rate_db = cpReturnRateTable()
        result = cp_return_rate_db.getTableResult(advisor)

        if result is None:
            print("Debug - CP Return Rate: No data returned from database")
            return {
                'json_data': [{
                    'datasets': [
                        {'data': ['0.00'], 'label': '12 Months Return Rate', 'chartId': '938'},
                        {'data': ['0.00'], 'label': '6 Months Return Rate', 'chartId': '938'}
                    ],
                    'labels': ['No Data']
                }]
            }

        print(f"Debug - CP Return Rate: Database returned: {type(result)}")

        # The database function should return the JSON data directly
        if isinstance(result, dict) and 'json_data' in result:
            print("Debug - CP Return Rate: Successfully got JSON data from database")
            return result
        elif isinstance(result, list) and len(result) > 0:
            # If it's a list, take the first element
            return {'json_data': result}
        else:
            # If the result is not in expected format, try to parse it
            print(f"Debug - CP Return Rate: Unexpected result format: {result}")
            return {
                'json_data': [{
                    'datasets': [
                        {'data': ['0.00'], 'label': '12 Months Return Rate', 'chartId': '938'},
                        {'data': ['0.00'], 'label': '6 Months Return Rate', 'chartId': '938'}
                    ],
                    'labels': ['Format Error']
                }]
            }

    except Exception as e:
        print(f"Error calculating CP return rate: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'json_data': [{
                'datasets': [
                    {'data': ['0.00'], 'label': '12 Months Return Rate', 'chartId': '938'},
                    {'data': ['0.00'], 'label': '6 Months Return Rate', 'chartId': '938'}
                ],
                'labels': ['Error']
            }]
        }

    except Exception as e:
        print(f"Error calculating CP return rate: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'json_data': [{
                'datasets': [
                    {'data': ['0.00'], 'label': '12 Months Return Rate', 'chartId': '938'},
                    {'data': ['0.00'], 'label': '6 Months Return Rate', 'chartId': '938'}
                ],
                'labels': ['Error']
            }]
        }

def get_cp_return_rate_summary(combined_revenue_details, advisor=None, target_month=None, customer_pay_types=None):
    """
    Get CP Return Rate summary for a specific month

    Args:
        combined_revenue_details: DataFrame with revenue details
        advisor: List of service advisors or None for all
        target_month: Target month in YYYY-MM format, defaults to most recent
        customer_pay_types: Set of customer pay types (e.g., {'C', 'E', 'M'})

    Returns:
        dict: Return rate summary with 6-month and 12-month rates
    """
    try:
        # Get the full return rate data
        return_rate_data = calculate_cp_return_rate(combined_revenue_details, advisor, customer_pay_types)

        # Extract the data
        datasets = return_rate_data['json_data'][0]['datasets']
        labels = return_rate_data['json_data'][0]['labels']

        if not labels or labels[0] in ['No Data', 'Error']:
            return {
                'six_month_rate': 0.00,
                'twelve_month_rate': 0.00,
                'month': 'No Data'
            }

        # Find target month index (default to most recent - index 0)
        index = 0
        if target_month:
            if target_month in labels:
                index = labels.index(target_month)

        return {
            'six_month_rate': float(datasets[1]['data'][index]),  # 6 Months Return Rate is second dataset
            'twelve_month_rate': float(datasets[0]['data'][index]),  # 12 Months Return Rate is first dataset
            'month': labels[index]
        }

    except Exception as e:
        print(f"Error getting CP return rate summary: {str(e)}")
        return {
            'six_month_rate': 0.00,
            'twelve_month_rate': 0.00,
            'month': 'Error'
        }

def initialize_empty_metrics():
    """Initialize empty metrics structure"""
    return {
        "one_line_metrics": {
            "under_60k": 0, "over_60k": 0, "total_shop": 0,
            "perc_under_60k": 0, "perc_over_60k": 0, "perc_total_shop": 0
        },
        "multi_line_metrics": {
            "under_60k": 0, "over_60k": 0, "total_shop": 0,
            "perc_under_60k": 0, "perc_over_60k": 0, "perc_total_shop": 0
        },
        "average_open_days": {
            "customer_pay": 0, "warranty": 0, "internal": 0,
            "extended_service": 0, "maintenance": 0, "factory_service": 0
        },
        "parts_to_labor_ratio": {
            "overall": 0, "competitive": 0, "maintenance": 0, "repair": 0
        },
        "labor_hours_percentage": {
            "customer_pay": 0, "warranty": 0, "internal": 0,
            "extended_service": 0, "maintenance": 0, "factory_service": 0
        },
        "mpi_penetration_percentage": 0,
        "menu_penetration_percentage": 0,
        "cp_return_rate": {
            "six_month_rate": 0.00,
            "twelve_month_rate": 0.00,
            "month": "No Data"
        }
    }

def db_execution_special_metrics(target_date_str, advisor, tech, retail_flag, columns_to_check):
    """Handle database operations and execute special metrics processing"""
    
    try:
        # Get target month date range
        month_start, month_end = get_month_date_range_from_target(target_date_str)
        
        # Fetch all data from database
        all_revenue_details_table_db_connect = allRevenueDetailsTable()
        all_revenue_details_df = all_revenue_details_table_db_connect.getTableResult()
        
        if all_revenue_details_df.empty:
            return None
        
        # Convert date column and other preprocessing
        columns_to_convert = ['lbrsale', 'lbrcost', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].replace(r'^\s*$', np.nan, regex=True)
        all_revenue_details_df[columns_to_convert] = all_revenue_details_df[columns_to_convert].apply(lambda x: pd.to_numeric(x.fillna(0), errors='coerce'))
        
        # Define customer and warranty pay types based on retail_flag
        if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C'}
            warranty_pay_types = {'W', 'F', 'M', 'E'}
        elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'M'}
            warranty_pay_types = {'W', 'F', 'E'}
        elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
            customer_pay_types = {'C', 'E'}
            warranty_pay_types = {'W', 'F', 'M'}
        elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
            customer_pay_types = {'C', 'E', 'M'}
            warranty_pay_types = {'W', 'F'}
        
        target_month_result = process_target_month_special_metrics(
            all_revenue_details_df, 
            month_start, 
            month_end,            
            advisor, 
            tech, 
            retail_flag, 
            customer_pay_types, 
            warranty_pay_types, 
            columns_to_check
        )
        
        return target_month_result, customer_pay_types, warranty_pay_types
        
    except Exception as e:
        print(f"ERROR in db_execution_special_metrics: {str(e)}")
        return None, None, None

def db_calculation_special_metrics():
    """Main execution function for special metrics calculation"""
    
    # Configuration variables
    columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
    retail_flag = {'C'}
    
    # Using the YYYY-MM format
    advisor_set = 'all'
    tech_set = 'all'
    TARGET_MONTHS_YEARS = ["2023-11"]  # Changed to YYYY-MM format
    
    # Process advisor configuration
    if isinstance(advisor_set, str):
        if advisor_set.lower() == 'all':
            advisor = {'all'}
        elif ',' in advisor_set:
            advisor = {x.strip() for x in advisor_set.split(',')}
        else:
            advisor = {advisor_set.strip()}
    else:
        advisor = {'all'}
    
    # Process technician configuration
    if isinstance(tech_set, str):
        if tech_set.lower() == 'all':
            tech = {'all'}
        elif ',' in tech_set:
            tech = {x.strip() for x in tech_set.split(',')}
        else:
            tech = {tech_set.strip()}
    else:
        tech = {'all'}
    
    # Execute database operations and processing
    target_date_str = TARGET_MONTHS_YEARS[0]
    target_month_result, customer_pay_types, warranty_pay_types = db_execution_special_metrics(
        target_date_str, advisor, tech, retail_flag, columns_to_check
    )
    # Process results
    if target_month_result:
        final_result_set = {
            "analysis_info": {
                "target_month": target_date_str,
                "analysis_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "advisor_filter": list(advisor),
                "technician_filter": list(tech),
                "customer_pay_types": list(customer_pay_types),
                "warranty_pay_types": list(warranty_pay_types)
            },
            "target_month_results": target_month_result
        }
        
        # Write results to JSON file
        output_filename = "chart_processing_results/special_metrics_calculated_value.json"
        with open(output_filename, 'w', encoding='utf-8') as json_file:
            json.dump(final_result_set, json_file, indent=4, ensure_ascii=False)
        
        print(f"\nSpecial metrics data written successfully to {output_filename}")
        
        # Display summary
        print(f"\nSpecial Metrics Summary for {target_month_result['target_month_name']}:")
        print(f"  Total ROs: {target_month_result['total_ros']}")
        print(f"  One-Line ROs: {target_month_result['special_metrics']['one_line_metrics']['total_shop']}")
        print(f"  Multi-Line ROs: {target_month_result['special_metrics']['multi_line_metrics']['total_shop']}")
        print(f"  Parts to Labor Ratio: {target_month_result['special_metrics']['parts_to_labor_ratio']['overall']}")
        
    else:
        print(f"No data available for target month {target_date_str}")

# Example usage
if __name__ == "__main__":
    db_calculation_special_metrics()