#!/usr/bin/env python3
"""
Debug CP Return Rate database function call
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_cp_return_rate_debug():
    """Test CP Return Rate database function to see what it actually returns"""
    
    print("=== CP Return Rate Database Debug ===\n")
    
    try:
        print("1. Testing database query generation...")
        from db_handler.db_query_handler import cpReturnRateTableQuery
        
        query_handler = cpReturnRateTableQuery()
        query = query_handler.generate_query(advisor=None)
        print(f"Generated query: {query}")
        
        print("\n2. Testing database connection...")
        from db_handler.db_connector import cpReturnRateTable
        
        cp_return_db = cpReturnRateTable()
        result = cp_return_db.getTableResult(advisor=None)
        
        print(f"\n3. Database result analysis:")
        print(f"   Result type: {type(result)}")
        print(f"   Result: {result}")
        
        if result is None:
            print("   ❌ No data returned from database")
            print("   This could be due to:")
            print("   - Database connection issues")
            print("   - Function doesn't exist")
            print("   - No data for the specified period")
            print("   - Environment variables not set")
        else:
            print("   ✅ Got data from database")
            
            if hasattr(result, 'columns'):
                print(f"   DataFrame columns: {list(result.columns)}")
                print(f"   DataFrame shape: {result.shape}")
                print(f"   DataFrame head:\n{result.head()}")
            elif isinstance(result, dict):
                print(f"   Dictionary keys: {list(result.keys())}")
                for key, value in result.items():
                    print(f"   {key}: {type(value)} = {value}")
            elif isinstance(result, list):
                print(f"   List length: {len(result)}")
                if result:
                    print(f"   First item type: {type(result[0])}")
                    print(f"   First item: {result[0]}")
        
        print("\n4. Testing main function...")
        from validate_special_metrics import calculate_cp_return_rate
        
        final_result = calculate_cp_return_rate(advisor=None)
        print(f"   Final result type: {type(final_result)}")
        print(f"   Final result: {final_result}")
        
        if isinstance(final_result, dict) and 'json_data' in final_result:
            json_data = final_result['json_data']
            if json_data and len(json_data) > 0:
                datasets = json_data[0].get('datasets', [])
                labels = json_data[0].get('labels', [])
                print(f"   Labels: {labels}")
                for dataset in datasets:
                    label = dataset.get('label', 'Unknown')
                    data = dataset.get('data', [])
                    print(f"   {label}: {data}")
        
        print("\n5. Testing summary function...")
        from validate_special_metrics import get_cp_return_rate_summary
        
        summary = get_cp_return_rate_summary(None, advisor=None)
        print(f"   Summary: {summary}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def check_database_function_exists():
    """Check if the database function exists"""
    
    print("\n=== Database Function Existence Check ===\n")
    
    try:
        import psycopg2
        from db_handler.db_credential_handler import db_credentail
        
        db_cred = db_credentail()
        db_params = {
            'host': db_cred.host,
            'database': db_cred.database,
            'user': db_cred.user,
            'password': db_cred.password,
            'port': db_cred.port
        }
        
        connection = psycopg2.connect(**db_params)
        cursor = connection.cursor()
        
        # Check if the function exists
        check_function_query = """
        SELECT EXISTS (
            SELECT 1 
            FROM pg_proc p 
            JOIN pg_namespace n ON p.pronamespace = n.oid 
            WHERE n.nspname = 'stateless_dbd_special_metrics' 
            AND p.proname = 'get_returnrate_by_service_advisor'
        );
        """
        
        cursor.execute(check_function_query)
        function_exists = cursor.fetchone()[0]
        
        print(f"Function exists: {function_exists}")
        
        if function_exists:
            print("✅ Database function exists")
            
            # Try to get function details
            function_details_query = """
            SELECT p.proname, p.proargtypes, p.prorettype, t.typname as return_type
            FROM pg_proc p 
            JOIN pg_namespace n ON p.pronamespace = n.oid 
            JOIN pg_type t ON p.prorettype = t.oid
            WHERE n.nspname = 'stateless_dbd_special_metrics' 
            AND p.proname = 'get_returnrate_by_service_advisor';
            """
            
            cursor.execute(function_details_query)
            details = cursor.fetchone()
            if details:
                print(f"Function name: {details[0]}")
                print(f"Return type: {details[3]}")
        else:
            print("❌ Database function does not exist")
            print("Please check:")
            print("1. Function name spelling")
            print("2. Schema name (stateless_dbd_special_metrics)")
            print("3. Database connection")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ Error checking database function: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_cp_return_rate_debug()
    check_database_function_exists()
