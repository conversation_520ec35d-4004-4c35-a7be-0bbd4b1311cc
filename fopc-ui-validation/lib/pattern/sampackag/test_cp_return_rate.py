#!/usr/bin/env python3
"""
Test script for CP Return Rate calculation
This demonstrates the logic without requiring database connection
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import json

def create_mock_cp_return_data():
    """Create mock customer pay data for testing return rate calculation"""
    data = []
    
    # Create data for multiple months with some returning VINs
    base_date = datetime(2023, 11, 1)
    
    # VINs that will be used across different periods
    vins = [
        'VIN001', 'VIN002', 'VIN003', 'VIN004', 'VIN005',
        'VIN006', 'VIN007', 'VIN008', 'VIN009', 'VIN010',
        'VIN011', 'VIN012', 'VIN013', 'VIN014', 'VIN015'
    ]
    
    ro_counter = 1000
    
    # Generate data for 18 months back from base_date
    for month_offset in range(18):
        month_date = base_date - relativedelta(months=month_offset)
        
        # Add some ROs for this month
        for i in range(5):  # 5 ROs per month
            vin = vins[i % len(vins)]
            
            data.append({
                'ronumber': str(ro_counter),
                'closeddate': month_date + timedelta(days=i*2),
                'store_id': '1234',
                'vin': vin,
                'department': 'Service',
                'hide_ro': '0',
                'serviceadvisor': 'John Doe',
                'opcategory': 'Repair',
                'lbrsale': 150.0 + (i * 25),
                'lbrsoldhours': 2.5 + i,
                'prtextendedsale': 75.0 + (i * 15),
                'prtextendedcost': 45.0 + (i * 8),
                'paytypegroup': 'C',
                'group': 'C',
                'unique_ro_number': f"{ro_counter}_{month_date.strftime('%Y-%m-%d')}"
            })
            ro_counter += 1
    
    # Add some "return" visits - same VINs coming back in subsequent months
    return_months = [0, 1, 2, 3]  # Recent months where returns happen
    for month_offset in return_months:
        month_date = base_date - relativedelta(months=month_offset)
        next_month = month_date + relativedelta(months=1)
        
        # Some VINs from previous periods return
        returning_vins = vins[:3]  # First 3 VINs return
        
        for i, vin in enumerate(returning_vins):
            data.append({
                'ronumber': str(ro_counter),
                'closeddate': next_month + timedelta(days=i*3),
                'store_id': '1234',
                'vin': vin,
                'department': 'Service',
                'hide_ro': '0',
                'serviceadvisor': 'John Doe',
                'opcategory': 'Repair',
                'lbrsale': 100.0 + (i * 20),
                'lbrsoldhours': 1.5 + i,
                'prtextendedsale': 50.0 + (i * 10),
                'prtextendedcost': 30.0 + (i * 5),
                'paytypegroup': 'C',
                'group': 'C',
                'unique_ro_number': f"{ro_counter}_{next_month.strftime('%Y-%m-%d')}"
            })
            ro_counter += 1
    
    return pd.DataFrame(data)

def mock_calculate_cp_return_rate(combined_revenue_details, advisor=None):
    """
    Mock version of calculate_cp_return_rate for testing
    """
    try:
        print("Starting CP Return Rate calculation...")
        
        # Get the last date from data
        last_date = combined_revenue_details['closeddate'].max()
        if isinstance(last_date, str):
            last_date = pd.to_datetime(last_date).date()
        elif hasattr(last_date, 'date'):
            last_date = last_date.date()
        
        print(f"Last date in data: {last_date}")
        
        # Create date ranges for 6 months (simplified to 3 periods for demo)
        date_ranges = []
        for i in range(3):
            end_date = (pd.to_datetime(last_date).replace(day=1) - relativedelta(months=i)).date()
            
            # 6-month period
            start_date_6 = end_date - relativedelta(months=6)
            next_month_end_6 = end_date + relativedelta(months=1) - timedelta(days=1)
            date_ranges.append({
                'start_date': start_date_6,
                'end_date': end_date - timedelta(days=1),
                'next_month_start': end_date,
                'next_month_end': next_month_end_6,
                'months': 6,
                'param': i
            })
            
            # 12-month period
            start_date_12 = end_date - relativedelta(months=12)
            next_month_end_12 = end_date + relativedelta(months=1) - timedelta(days=1)
            date_ranges.append({
                'start_date': start_date_12,
                'end_date': end_date - timedelta(days=1),
                'next_month_start': end_date,
                'next_month_end': next_month_end_12,
                'months': 12,
                'param': i
            })
        
        # Filter customer pay data
        customer_pay_filter = (
            (combined_revenue_details['department'] == 'Service') &
            (combined_revenue_details['hide_ro'] == '0') &
            (combined_revenue_details['group'] == 'C') &
            (combined_revenue_details['opcategory'] != 'N/A') &
            ~((combined_revenue_details['lbrsale'].fillna(0) == 0) & 
              (combined_revenue_details['lbrsoldhours'].fillna(0) == 0) & 
              (combined_revenue_details['prtextendedsale'].fillna(0) == 0) & 
              (combined_revenue_details['prtextendedcost'].fillna(0) == 0))
        )
        
        cp_data = combined_revenue_details[customer_pay_filter].copy()
        
        if cp_data.empty:
            print("No customer pay data found")
            return {'json_data': [{'datasets': [{'data': ['0.00'], 'label': '12 Months Return Rate', 'chartId': '938'}, {'data': ['0.00'], 'label': '6 Months Return Rate', 'chartId': '938'}], 'labels': ['No Data']}]}
        
        print(f"Customer pay data: {len(cp_data)} records")
        
        # Convert dates
        cp_data['closeddate'] = pd.to_datetime(cp_data['closeddate'])
        
        # Calculate return rates for each period
        results = []
        
        for period in range(3):  # Process 3 periods for demo
            six_month_data = [dr for dr in date_ranges if dr['months'] == 6 and dr['param'] == period]
            twelve_month_data = [dr for dr in date_ranges if dr['months'] == 12 and dr['param'] == period]
            
            if not six_month_data or not twelve_month_data:
                continue
                
            six_range = six_month_data[0]
            twelve_range = twelve_month_data[0]
            
            print(f"\nPeriod {period}:")
            print(f"6-month range: {six_range['start_date']} to {six_range['end_date']}")
            print(f"12-month range: {twelve_range['start_date']} to {twelve_range['end_date']}")
            print(f"Next month: {six_range['next_month_start']} to {six_range['next_month_end']}")
            
            # Get VINs in the 6-month period
            six_month_vins = cp_data[
                (cp_data['closeddate'].dt.date >= six_range['start_date']) &
                (cp_data['closeddate'].dt.date <= six_range['end_date'])
            ]['vin'].unique()
            
            # Get VINs in the 12-month period  
            twelve_month_vins = cp_data[
                (cp_data['closeddate'].dt.date >= twelve_range['start_date']) &
                (cp_data['closeddate'].dt.date <= twelve_range['end_date'])
            ]['vin'].unique()
            
            print(f"6-month VINs: {len(six_month_vins)} unique VINs")
            print(f"12-month VINs: {len(twelve_month_vins)} unique VINs")
            
            # Get VINs that returned in the next month after 6-month period
            six_month_returns = cp_data[
                (cp_data['closeddate'].dt.date >= six_range['next_month_start']) &
                (cp_data['closeddate'].dt.date <= six_range['next_month_end']) &
                (cp_data['vin'].isin(six_month_vins))
            ]['vin'].nunique()
            
            # Get VINs that returned in the next month after 12-month period
            twelve_month_returns = cp_data[
                (cp_data['closeddate'].dt.date >= twelve_range['next_month_start']) &
                (cp_data['closeddate'].dt.date <= twelve_range['next_month_end']) &
                (cp_data['vin'].isin(twelve_month_vins))
            ]['vin'].nunique()
            
            print(f"6-month returns: {six_month_returns} VINs")
            print(f"12-month returns: {twelve_month_returns} VINs")
            
            # Calculate return rates
            six_month_rate = round((six_month_returns / len(six_month_vins)) * 100, 2) if len(six_month_vins) > 0 else 0.00
            twelve_month_rate = round((twelve_month_returns / len(twelve_month_vins)) * 100, 2) if len(twelve_month_vins) > 0 else 0.00
            
            print(f"6-month return rate: {six_month_rate}%")
            print(f"12-month return rate: {twelve_month_rate}%")
            
            results.append({
                'six_month_rate': six_month_rate,
                'twelve_month_rate': twelve_month_rate,
                'month_year': six_range['next_month_start'].strftime('%Y-%m')
            })
        
        # Sort by month_year descending
        results.sort(key=lambda x: x['month_year'], reverse=True)
        
        if not results:
            return {'json_data': [{'datasets': [{'data': ['0.00'], 'label': '12 Months Return Rate', 'chartId': '938'}, {'data': ['0.00'], 'label': '6 Months Return Rate', 'chartId': '938'}], 'labels': ['No Data']}]}
        
        # Format output
        twelve_month_data = [str(r['twelve_month_rate']) for r in results]
        six_month_data = [str(r['six_month_rate']) for r in results]
        labels = [r['month_year'] for r in results]
        
        return {
            'json_data': [{
                'datasets': [
                    {'data': twelve_month_data, 'label': '12 Months Return Rate', 'chartId': '938'},
                    {'data': six_month_data, 'label': '6 Months Return Rate', 'chartId': '938'}
                ],
                'labels': labels
            }]
        }
        
    except Exception as e:
        print(f"Error calculating CP return rate: {str(e)}")
        import traceback
        traceback.print_exc()
        return {'json_data': [{'datasets': [{'data': ['0.00'], 'label': '12 Months Return Rate', 'chartId': '938'}, {'data': ['0.00'], 'label': '6 Months Return Rate', 'chartId': '938'}], 'labels': ['Error']}]}

def main():
    """Test the CP Return Rate calculation"""
    print("=== Testing CP Return Rate Calculation ===\n")
    
    # Create mock data
    print("1. Creating mock customer pay data with return visits...")
    cp_data = create_mock_cp_return_data()
    
    print(f"Total records: {len(cp_data)}")
    print(f"Unique VINs: {cp_data['vin'].nunique()}")
    print(f"Date range: {cp_data['closeddate'].min()} to {cp_data['closeddate'].max()}")
    
    print("\nSample data:")
    print(cp_data[['ronumber', 'closeddate', 'vin', 'lbrsale']].head(10))
    
    # Test the calculation
    print("\n2. Testing CP Return Rate calculation...")
    result = mock_calculate_cp_return_rate(cp_data, advisor=['All'])
    
    print(f"\n3. Final Result:")
    print(json.dumps(result, indent=2))
    
    # Extract and display the rates
    datasets = result['json_data'][0]['datasets']
    labels = result['json_data'][0]['labels']
    
    print(f"\n4. Return Rate Summary:")
    for i, month in enumerate(labels):
        twelve_month_rate = datasets[0]['data'][i]
        six_month_rate = datasets[1]['data'][i]
        print(f"  {month}: 12-month rate = {twelve_month_rate}%, 6-month rate = {six_month_rate}%")
    
    print("\n✅ CP Return Rate calculation completed successfully!")
    print("This function calculates the percentage of vehicles that return for service")
    print("within the next month after being serviced in the previous 6 or 12 months.")

if __name__ == "__main__":
    main()
