# CP Return Rate Implementation

## Overview

I've implemented a Python function `calculate_cp_return_rate()` that matches the PostgreSQL database function `get_returnrate_by_service_advisor()`. This function calculates customer pay return rates for both 6-month and 12-month periods.

## What is Return Rate?

Return Rate measures the percentage of vehicles that return for service within the next month after being serviced in a previous period (6 or 12 months). It's a key metric for measuring customer satisfaction and service quality.

## Database Function Analysis

The PostgreSQL function does the following:

1. **Creates date ranges** for 18 months (0-17) for both 6 and 12 month periods
2. **Filters customer pay data** from `all_revenue_details` with specific criteria:
   - Department = 'Service'
   - hide_ro = '0'
   - Customer pay types (mapped_paytype = 'C')
   - opcategory ≠ 'N/A'
   - Has revenue/cost values (not all zeros)
3. **Calculates return rates** by:
   - Finding unique VINs in each period (6 or 12 months)
   - Checking which VINs return in the following month
   - Computing percentage: (returned VINs / total VINs) × 100
4. **Returns JSON data** with 12-month and 6-month return rates by month

## Python Implementation

### Main Function: `calculate_cp_return_rate()`

```python
def calculate_cp_return_rate(combined_revenue_details, advisor=None):
    """
    Calculate Customer Pay Return Rate matching the database function logic
    
    Args:
        combined_revenue_details: DataFrame with revenue details
        advisor: List of service advisors or None for all
    
    Returns:
        dict: JSON data structure with 6-month and 12-month return rates by month
    """
```

### Key Features:

1. **Date Range Generation**: Creates 18 months of date ranges for both 6 and 12 month periods
2. **Customer Pay Filtering**: Filters data to match database criteria
3. **VIN-based Analysis**: Tracks unique vehicles across time periods
4. **Return Rate Calculation**: Computes percentages for vehicles returning in subsequent months
5. **JSON Output**: Returns data in the same format as the database function

### Helper Function: `get_cp_return_rate_summary()`

```python
def get_cp_return_rate_summary(combined_revenue_details, advisor=None, target_month=None):
    """
    Get CP Return Rate summary for a specific month
    
    Returns:
        dict: Return rate summary with 6-month and 12-month rates
    """
```

This function extracts return rate data for a specific month, useful for dashboard displays.

## Integration with Special Metrics

The CP Return Rate has been integrated into the special metrics calculation:

1. **Added to `calculate_special_metrics()`**: Calls `get_cp_return_rate_summary()`
2. **Added to `initialize_empty_metrics()`**: Includes default return rate structure
3. **Output includes**: Both 6-month and 12-month return rates with the target month

## Output Format

The function returns data in the same JSON format as the database function:

```json
{
  "json_data": [{
    "datasets": [
      {
        "data": ["5.25", "4.80", "6.10"],
        "label": "12 Months Return Rate",
        "chartId": "938"
      },
      {
        "data": ["3.15", "2.90", "3.45"],
        "label": "6 Months Return Rate", 
        "chartId": "938"
      }
    ],
    "labels": ["2023-11", "2023-10", "2023-09"]
  }]
}
```

For special metrics integration, it also provides a summary format:

```json
{
  "cp_return_rate": {
    "six_month_rate": 3.15,
    "twelve_month_rate": 5.25,
    "month": "2023-11"
  }
}
```

## Algorithm Logic

1. **Period Definition**:
   - 6-month period: 6 months before target month
   - 12-month period: 12 months before target month
   - Next month: The month immediately following the period end

2. **VIN Tracking**:
   - Collect unique VINs serviced in each period
   - Check which VINs return for service in the next month
   - Calculate percentage of returning VINs

3. **Return Rate Formula**:
   ```
   Return Rate = (VINs returning in next month / Total VINs in period) × 100
   ```

## Key Differences from Database Function

1. **Simplified Configuration**: Uses data max date instead of configuration table lookup
2. **Streamlined Logic**: Combines similar operations to reduce code complexity
3. **Error Handling**: Includes comprehensive error handling and fallbacks
4. **Integration Ready**: Designed to work with existing special metrics framework

## Usage Examples

### Full Return Rate Data
```python
return_data = calculate_cp_return_rate(combined_revenue_details, advisor=['John Doe'])
```

### Summary for Dashboard
```python
summary = get_cp_return_rate_summary(combined_revenue_details, target_month='2023-11')
print(f"6-month return rate: {summary['six_month_rate']}%")
print(f"12-month return rate: {summary['twelve_month_rate']}%")
```

### Integration in Special Metrics
The return rate is automatically calculated when calling the main special metrics function and included in the output.

## Testing

A test script `test_cp_return_rate.py` is provided that:
- Creates mock customer pay data with return visits
- Demonstrates the calculation logic
- Shows expected output format
- Validates the algorithm with sample data

## Files Modified

1. **`validate_special_metrics.py`**:
   - Added `calculate_cp_return_rate()` function
   - Added `get_cp_return_rate_summary()` helper function
   - Updated `calculate_special_metrics()` to include return rate
   - Updated `initialize_empty_metrics()` with return rate structure

2. **`test_cp_return_rate.py`** (new):
   - Test script demonstrating the functionality
   - Mock data generation for testing
   - Example usage and output validation

The implementation provides a complete, streamlined solution for calculating CP Return Rate that matches the database function logic while being optimized for the Python environment.
