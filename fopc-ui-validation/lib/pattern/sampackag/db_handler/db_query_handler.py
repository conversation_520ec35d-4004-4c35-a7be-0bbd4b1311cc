import os
from datetime import datetime
from dateutil.relativedelta import relativedelta
from dotenv import load_dotenv
load_dotenv()
store_id_set = (os.environ.get('store_id')).strip()
s_date_env = os.environ.get('start_date')
e_date_env = os.environ.get('end_date')
fopc_month = os.environ.get('fopc_month')
pre_fopc_month = os.environ.get('pre_fopc_month')

if store_id_set == 'all':
    store_id = 'all'
else:
    if ',' in store_id_set:
        store_id = f"({', '.join(repr(x.strip()) for x in store_id_set.split(','))})"
    else:
        store_id = f"('{store_id_set.strip()}')"

last_month = os.environ.get('last_month')
if last_month is not None:
    last_month_date = datetime.strptime(last_month, "%Y-%m")
    date_ranges = [(last_month_date - relativedelta(months=i)).strftime("%Y-%m") for i in range(3)]
    date_ranges_sql = "({})".format(", ".join([f"'{date}'" for date in date_ranges]))
# def get_store_id():
#     store_id_set = store_id_set

#     if store_id_set is None:
#         raise ValueError("store_id is not set in config")

#     if store_id_set == 'all':
#         return 'all'
#     elif ',' in store_id_set:
#         return f"({', '.join(repr(x.strip()) for x in store_id_set.split(','))})"
#     else:
#         return f"('{store_id_set.strip()}')"

class OpcodeQuerygenerator:
    def generate_query(self):
        sql_query = f"SELECT department,opcode,opcategory,store_id,grid_excluded FROM stateful_cc_physical_rw.ro_opcodes WHERE store_id in {store_id};"
        return sql_query

class payTypeFixedRateUpdateStatus:
    def generate_query(self):
        getOpcodePaytypeUpdateTable = f"SELECT id, opcode, pay_type, start_date, end_date, store_id FROM stateful_cc_physical_rw.fixed_rate_enable_disable_master_log WHERE id IS NOT NULL AND store_id in {store_id};"
        return getOpcodePaytypeUpdateTable

class payTypeFixedRateTable:
    def generate_query(self):
        getPayTypeFixedRate = f"SELECT id, paytype, labor_fixedratevalue, parts_fixedratevalue, store_id, fixedratedate FROM stateful_cc_physical_rw.fixed_rate_master_paytype WHERE store_id in {store_id};"
        return getPayTypeFixedRate
class opcodeFixedRateTable:
    def generate_query(self):
        getOpcodeFixedRate = f"SELECT id, opcode, paytype, fixed_rate_value, store_id, fixed_rate_date FROM stateful_cc_physical_rw.fixed_rate_master WHERE store_id in {store_id};"
        return getOpcodeFixedRate
class gridDataTable:
    def generate_query(self):
        getGridDataTable = f"SELECT hours, col_0, col_1, col_2, col_3, col_4, col_5, col_6, col_7, col_8, col_9, store_id, created_date, door_rate, grid_type FROM stateful_cc_physical_rw.griddata_dtl WHERE store_id in {store_id};"
        return getGridDataTable
class OpcodeTableQuery:
    def generate_query(self):
        sql_query = f"SELECT department,opcode,opcategory,grid_excluded,store_id, mpi_item FROM stateful_cc_physical_rw.ro_opcodes WHERE store_id in {store_id};"
        return sql_query
class partInventoryDetailsQuery:
    def generate_query(self):
        getPartInventoryDetailsTable = f"SELECT id,partno,part_source,list_price,store_id  FROM stateful_atm_source_raw.parts_inventory_details WHERE store_id in {store_id};"
        return getPartInventoryDetailsTable
class MenuOpcodes:
    def get_menu_opcodes(self):
        sql_query = f"SELECT opcode FROM stateful_cc_physical_rw.menu_opcodes where store_id in {store_id};"
        return sql_query
class payTypeMasterTableQuery:
    def generate_query(self):
        sql_query = f"SELECT pay_type,department,store_id FROM stateful_cc_physical_rw.pay_type_master WHERE store_id in {store_id};"
        return sql_query
class menuMasterTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.menu_master WHERE store_id in {store_id};"
        return sql_query
class menuServiceTypeTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.menu_service_type WHERE store_id in {store_id};"
        return sql_query
class assignedMenuModelsTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.assigned_menu_models WHERE store_id in {store_id};"
        return sql_query
class assignedMenuOpcodesTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.assigned_menu_opcodes WHERE store_id in {store_id};"
        return sql_query
class MPISetupTableQuery:
    def generate_query(self):
        sql_query = f"SELECT frh,is_active FROM stateful_cc_physical_rw.mpi_setup WHERE store_id in {store_id};"
        return sql_query
class MPIOpcodesTableQuery:
    def generate_query(self):
        sql_query = f"SELECT opcode FROM stateful_cc_physical_rw.mpi_opcodes WHERE store_id in {store_id};"
        return sql_query
class fleetCustomerFixedRateTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.labor_grid_fleet_account WHERE store_id in {store_id};"
        return sql_query
class fleetPayTypeFixedRateTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.labor_grid_fleet_paytype WHERE store_id in {store_id};"
        return sql_query
class fleetOpcodeFixedRateTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.labor_grid_fleet_opcode WHERE store_id in {store_id};"
        return sql_query
class gridDataDetailsQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_physical_rw.griddata_dtl WHERE store_id in {store_id};"
        return sql_query
class allRevenueDetailsTableQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE store_id in {store_id} and closeddate >= '{s_date_env}' and closeddate <= '{e_date_env}';"
        return sql_query
class allRevenueDetailsCPOverviewQuery:
    def generate_query(self):
        sql_query = f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE store_id in {store_id};"
        return sql_query
if store_id == 'all':
    class allRevenueDetailsForClientReportCardQuery:
        def generate_query(self):
            sql_query = f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE month_year IN ('{fopc_month}' ,'{pre_fopc_month}');"
            return sql_query
else:
    class allRevenueDetailsForClientReportCardQuery:
        def generate_query(self):
            sql_query = f"SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE  store_id in {store_id} and month_year IN ('{fopc_month}' ,'{pre_fopc_month}');"
            return sql_query

if store_id == 'all':
    class allRevenueDetailsForClientReportCard3MonthQuery:
        def generate_query(self):
            sql_query = f"""SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE ((month_year >= '{s_date_env}' AND month_year <= '{e_date_env}') OR month_year IN {date_ranges_sql});"""
            return sql_query
else:
    class allRevenueDetailsForClientReportCard3MonthQuery:
        def generate_query(self):
            sql_query = f"""SELECT * FROM stateful_cc_aggregate.all_revenue_details WHERE store_id in {store_id} AND ((month_year >= '{s_date_env}' AND month_year <= '{e_date_env}') OR month_year IN {date_ranges_sql});"""
            return sql_query

class getCustomerPayTypeGroups:
    def generate_query(self):
        sql_ruery = f"select source_paytype from stateful_cc_physical_rw.paytype_retail_flag_setting prfs where store_id in {store_id} and mapped_paytype = 'C'"
        return sql_ruery


class allRevenueDetailsTableQuery:
    def generate_query(self):
        # Calculate end date as 12 months from start date
        from datetime import datetime
        from dateutil.relativedelta import relativedelta
        store_id_sql = store_id
        # Parse start date
        start_date = datetime.strptime(s_date_env, '%Y-%m-%d')
        
        # Calculate end date (12 months from start date)
        end_date = start_date + relativedelta(months=12) - relativedelta(days=1)
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        if store_id_sql == 'all':
            return (f"SELECT * FROM stateful_cc_aggregate.all_revenue_details "
                    f"WHERE closeddate >= '{start_date}' AND closeddate <= '{end_date}';")
        else:
            return (f"SELECT * FROM stateful_cc_aggregate.all_revenue_details "
                    f"WHERE store_id IN {store_id_sql} "
                    f"AND closeddate >= '{start_date}' AND closeddate <= '{end_date}';")

class totalDetailsTableQuery:# TO:DO===Need to Refactor
    def generate_query(self):
        # Query to get shop supplies data from total_details table
        # from datetime import datetime
        # from dateutil.relativedelta import relativedelta
        store_id_sql = store_id
        # Parse start date
        start_date = datetime.strptime(s_date_env, '%Y-%m-%d')

        # Calculate end date (12 months from start date)
        end_date = start_date + relativedelta(months=12) - relativedelta(days=1)
        end_date_str = end_date.strftime('%Y-%m-%d')

        if store_id_sql == 'all':
            return (f"SELECT ronumber, closeddate, store_id, totalshopsupply "
                    f"FROM stateful_cc_ingest.total_details "
                    f"WHERE closeddate >= '{start_date}' AND closeddate <= '{end_date}';")
        else:
            return (f"SELECT ronumber, closeddate, store_id, totalshopsupply "
                    f"FROM stateful_cc_ingest.total_details "
                    f"WHERE store_id IN {store_id_sql} "
                    f"AND closeddate >= '{start_date}' AND closeddate <= '{end_date}';")

class cpReturnRateTableQuery:# CP Return Rate Query
    def generate_query(self, advisor=None):
        """Generate query for CP Return Rate using the database function"""

        # Convert advisor list to PostgreSQL array format
        if advisor is None or advisor == ['All'] or advisor == {'all'}:
            advisor_array = "ARRAY['All']"
        else:
            # Convert advisor list to PostgreSQL array format
            advisor_list = [f"'{adv}'" for adv in advisor]
            advisor_array = f"ARRAY[{','.join(advisor_list)}]"

        # Call the database function
        query = f"""
        SELECT * FROM stateless_dbd_special_metrics.get_returnrate_by_service_advisor({advisor_array});
        """

        return query

# Alternative approach if you want to keep the original structure
# and just modify the date range calculation
class allRevenueDetailsTableQueryYearly:
    def __init__(self, start_date_env, store_id):
        self.start_date_env = start_date_env
        self.store_id
