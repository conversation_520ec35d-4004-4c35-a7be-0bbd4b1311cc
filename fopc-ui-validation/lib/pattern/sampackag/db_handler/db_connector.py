
# from .db_query_handler import OpcodeQuerygenerator
# from dotenv import load_dotenv
# import os
# import psycopg2
# import sys

# from db_handler.db_credential_handler import db_credentail

# sys.path.append('C:/Users/<USER>/Desktop/fixedops_data_validation')

# load_dotenv()

# db_name = os.getenv("db_name")
# db_user = os.getenv("user")
# db_password = os.getenv("password")
# db_host = os.getenv("host")
# db_port = os.getenv("port")

# db_cred = db_credentail()
# credentials = db_cred.db_credentail_handler()
# credentail = credentials[0]
                
# db_params = {
#     'dbname': credentail['db_name'],
#     'user': credentail['db_user'],
#     'password': credentail['db_password'],
#     'host': credentail['db_host'],
#     'port': credentail['db_port']
# }


# class DbConnector:

#     def DbH<PERSON><PERSON>(self):

#         try:
           
#             department = None
            
#             connection = psycopg2.connect(**db_params)
#             cursor = connection.cursor()

#             query_generator = OpcodeQuerygenerator()
#             dpmt_query = query_generator.generate_qGuery()

#             cursor.execute(dpmt_query)
#             rows = cursor.fetchall()

#             department_arr = []
#             for row in rows:
#                 department, opcode, opcategory = row
#                 data = f'Department: {department}, Opcode: {opcode}, Opcategory:{opcategory}'
#                 department_arr.append(data)

#             return department_arr

#         except (Exception, psycopg2.Error) as error:
#             print("Error while connecting to PostgreSQL:", error)

#         finally:
#             if connection:
#                 cursor.close()
#                 connection.close()
import os
import sys
# current_dir = os.path.dirname(os.path.abspath(__file__))
# data_validation_path = os.path.join(current_dir, 'fixedops_data_validation')
# print(data_validation_path)
sys.path.append('../')
import psycopg2
import sys
import pandas as pd

from .db_query_handler import OpcodeQuerygenerator, payTypeFixedRateUpdateStatus, payTypeFixedRateTable, opcodeFixedRateTable, gridDataTable, OpcodeTableQuery, partInventoryDetailsQuery, MenuOpcodes, payTypeMasterTableQuery, menuMasterTableQuery, menuServiceTypeTableQuery, assignedMenuModelsTableQuery, assignedMenuOpcodesTableQuery, MPISetupTableQuery,MPIOpcodesTableQuery, fleetCustomerFixedRateTableQuery, fleetPayTypeFixedRateTableQuery, fleetOpcodeFixedRateTableQuery, gridDataDetailsQuery,allRevenueDetailsTableQuery, allRevenueDetailsForClientReportCardQuery, allRevenueDetailsForClientReportCard3MonthQuery, getCustomerPayTypeGroups,allRevenueDetailsCPOverviewQuery, totalDetailsTableQuery, cpReturnRateTableQuery
from .db_credential_handler import db_credentail

db_cred = db_credentail()
credentials = db_cred.db_credentail_handler()
credentail = credentials[0]

db_params = {
            'dbname': credentail['db_name'],
            'user': credentail['db_user'],
            'password': credentail['db_password'],
            'host': credentail['db_host'],
            'port': credentail['db_port']
        }

class DbConnector:

    def DbHandler(self):
        
        department = None
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query_generator = OpcodeQuerygenerator()
            dpmt_query = query_generator.generate_query()

            cursor.execute(dpmt_query)
            rows = cursor.fetchall()
            
            department_arr = []
            for row in rows:
                department, opcode, opcategory, store_id, grid_excluded = row
                data = f'Department: {department}, Opcode: {opcode}, Opcategory:{opcategory},store_id:{store_id},grid_excluded:{grid_excluded}'
                department_arr.append(data)
            return department_arr
            

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)

        finally:
            if connection:
                cursor.close()
                connection.close()

class opcodePayTypeFixedRateStatus:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = payTypeFixedRateUpdateStatus()
            opcodePayTypeQuery = query.generate_query()

            cursor.execute(opcodePayTypeQuery)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()

            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        opcodePayTypeFixedRateStatus_Table = pd.DataFrame(result, columns=column_names)
        return opcodePayTypeFixedRateStatus_Table

class payTypeFixedRates:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = payTypeFixedRateTable()
            payTypeFixedRateQuery = query.generate_query()

            cursor.execute(payTypeFixedRateQuery)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()
            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        payTypeFixedRate_Table = pd.DataFrame(result, columns=column_names)
        return payTypeFixedRate_Table

class opcodeFixedRates:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = opcodeFixedRateTable()
            opcodeFixedRateQuery = query.generate_query()

            cursor.execute(opcodeFixedRateQuery)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()
            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        opcodeFixedRate_Table = pd.DataFrame(result, columns=column_names)
        print(opcodeFixedRate_Table)
        return opcodeFixedRate_Table
class gridData:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = gridDataTable()
            getGridDataQuery = query.generate_query()

            cursor.execute(getGridDataQuery)

            # for desc in cursor.description:
            #     column_names.append(desc[0])

            rows = cursor.fetchall()
            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        gridData_Table = pd.DataFrame(result)
        print(gridData_Table)
        return gridData_Table
class opcodeTable:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = OpcodeTableQuery()
            opcodeTableQuery = query.generate_query()

            cursor.execute(opcodeTableQuery)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()
            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        opcodeTable = pd.DataFrame(result, columns=column_names)
        #opcodeTable.to_csv('../Output/RO_Opcodes.csv', index=False)
        
        return opcodeTable

class partInventoryTable:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = partInventoryDetailsQuery()
            partInventoryTableQuery= query.generate_query()

            cursor.execute(partInventoryTableQuery)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()
            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        partInventoryTable = pd.DataFrame(result, columns=column_names)
        #opcodeTable.to_csv('../Output/RO_Opcodes.csv', index=False)
        
        return partInventoryTable
class MenuOpcodesList:             
    def menu_opcode_list(self):
        connection = None
        cursor = None
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = MenuOpcodes()
            targetHoursPerRo = query.get_menu_opcodes()

            cursor.execute(targetHoursPerRo)
            result =  cursor.fetchone()
            
            return result
        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

class payTypeMasterTableResult:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = payTypeMasterTableQuery()
            payTypeMasterTable = query.generate_query()

            cursor.execute(payTypeMasterTable)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()

            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        payTypeMasterTableResult = pd.DataFrame(result, columns=column_names)
        return payTypeMasterTableResult

class menuMasterTableResult:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = menuMasterTableQuery()
            menuMasterTable = query.generate_query()

            cursor.execute(menuMasterTable)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()

            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        menuMasterTableResult = pd.DataFrame(result, columns=column_names)
        return menuMasterTableResult

class menuServiceTypeTableResult:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = menuServiceTypeTableQuery()
            menuServiceTypeTable = query.generate_query()

            cursor.execute(menuServiceTypeTable)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()

            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        menuServiceTypeTableResult = pd.DataFrame(result, columns=column_names)
        return menuServiceTypeTableResult

class assignedMenuModelsTableResult:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = assignedMenuModelsTableQuery()
            assignedMenuModelsTable = query.generate_query()

            cursor.execute(assignedMenuModelsTable)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()

            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        assignedMenuModelsTableResult = pd.DataFrame(result, columns=column_names)
        return assignedMenuModelsTableResult

class assignedMenuOpcodesTableResult:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = assignedMenuOpcodesTableQuery()
            assignedMenuOpcodesTable = query.generate_query()

            cursor.execute(assignedMenuOpcodesTable)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()

            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        assignedMenuOpcodesTableResult = pd.DataFrame(result, columns=column_names)
        return assignedMenuOpcodesTableResult
class MPISetupTableResult:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = MPISetupTableQuery()
            MPISetupTable = query.generate_query()

            cursor.execute(MPISetupTable)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()

            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        MPISetupTableResult = pd.DataFrame(result, columns=column_names)
        return MPISetupTableResult
class MPIOpcodesTableResult:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = set()
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = MPIOpcodesTableQuery()
            MPIOpcodesTable = query.generate_query()

            cursor.execute(MPIOpcodesTable)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()
            #print("  ---   rows --- ", rows)

            for row in rows:
                result.add(row[0])

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        # MPISetupTableResult = pd.DataFrame(result, columns=column_names)
        return result

class FleetCustomerFixedRateTableResult:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = fleetCustomerFixedRateTableQuery()
            FleetCustomerFixedRateTable = query.generate_query()

            cursor.execute(FleetCustomerFixedRateTable)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()

            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        FleetCustomerFixedRateTableResult = pd.DataFrame(result, columns=column_names)
        return FleetCustomerFixedRateTableResult

class FleetPayTypeFixedRateTableResult:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = fleetPayTypeFixedRateTableQuery()
            FleetPayTypeFixedRateTable = query.generate_query()

            cursor.execute(FleetPayTypeFixedRateTable)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()

            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        FleetPayTypeFixedRateTableResult = pd.DataFrame(result, columns=column_names)
        return FleetPayTypeFixedRateTableResult

class FleetOpcodeFixedRateTableResult:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = fleetOpcodeFixedRateTableQuery()
            FleetOpcodeFixedRateTable = query.generate_query()

            cursor.execute(FleetOpcodeFixedRateTable)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()

            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        FleetOpcodeFixedRateTableResult = pd.DataFrame(result, columns=column_names)
        return FleetOpcodeFixedRateTableResult
class gridDataDetailsTableResult:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = gridDataDetailsQuery()
            gridDataDetailsTable = query.generate_query()

            cursor.execute(gridDataDetailsTable)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()

            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        gridDataDetailsTableResult = pd.DataFrame(result, columns=column_names)
        return gridDataDetailsTableResult

class allRevenueDetailsTable:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = allRevenueDetailsTableQuery()
            allRevenueDetailsQuery = query.generate_query()

            cursor.execute(allRevenueDetailsQuery)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()
            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        allRevenueDetails_Table = pd.DataFrame(result, columns=column_names)
        return allRevenueDetails_Table

class allRevenueDetailsForClientReportCard:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = allRevenueDetailsForClientReportCardQuery()
            allRevenueDetailsQuery = query.generate_query()

            cursor.execute(allRevenueDetailsQuery)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()
            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        allRevenueDetails_Table = pd.DataFrame(result, columns=column_names)
        return allRevenueDetails_Table

class allRevenueDetailsForClientReportCard3Month:
    def getTableResult(self):
        
        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = allRevenueDetailsForClientReportCard3MonthQuery()
            allRevenueDetailsQuery = query.generate_query()
            print(allRevenueDetailsQuery)
            
            cursor.execute(allRevenueDetailsQuery)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()
            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        allRevenueDetails_Table = pd.DataFrame(result, columns=column_names)
        return allRevenueDetails_Table

class getCustomerPayTypeGroupsList:             
    def getCustomerPayTypeList(self):
        connection = None
        cursor = None
        result = set()
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = getCustomerPayTypeGroups()
            customerPayTypes = query.generate_query()
            
            cursor.execute(customerPayTypes)
            
            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()
            
            for row in rows:
                result.add(row[0])
            
        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)
        
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        return result


class allRevenueDetailsCPOverview:
    def getTableResult(self):

        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = allRevenueDetailsCPOverviewQuery()
            allRevenueDetailsQuery = query.generate_query()

            cursor.execute(allRevenueDetailsQuery)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()
            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)

        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        allRevenueDetails_Table = pd.DataFrame(result, columns=column_names)
        return allRevenueDetails_Table

class totalDetailsTable:
    def getTableResult(self):

        connection = None
        cursor = None
        result = []
        column_names = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = totalDetailsTableQuery()
            totalDetailsQuery = query.generate_query()

            cursor.execute(totalDetailsQuery)

            for desc in cursor.description:
                column_names.append(desc[0])

            rows = cursor.fetchall()
            result.extend(rows)

        except (Exception, psycopg2.Error) as error:
            print("Error while connecting to PostgreSQL:", error)

        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
        totalDetails_Table = pd.DataFrame(result, columns=column_names)
        return totalDetails_Table

class cpReturnRateTable:
    def getTableResult(self, advisor=None):
        """Get CP Return Rate data from database function"""

        connection = None
        cursor = None
        result = []
        try:
            connection = psycopg2.connect(**db_params)
            cursor = connection.cursor()

            query = cpReturnRateTableQuery()
            cpReturnRateQuery = query.generate_query(advisor)

            cursor.execute(cpReturnRateQuery)
            rows = cursor.fetchall()

            # The database function returns JSON data
            # Extract the JSON from the first column of each row
            for row in rows:
                if row[0]:  # Check if json_data is not null
                    result.append(row[0])

        except (Exception, psycopg2.Error) as error:
            print(f"Error while connecting to PostgreSQL for CP Return Rate: {error}")
            return None

        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

        # Return the first result (most recent) or None if no data
        return result[0] if result else None

