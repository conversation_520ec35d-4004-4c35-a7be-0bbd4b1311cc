#!/usr/bin/env python3
"""
Test script to verify the shop supplies fix
This script simulates the database data to test the logic without requiring database connection
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

def create_mock_total_details():
    """Create mock total_details data for testing"""
    # Create sample data that matches the database structure
    data = []
    
    # Sample RO numbers and dates
    ro_numbers = ['987067', '994077', '995910', '996045', '996065']
    dates = ['2023-11-01', '2023-11-02', '2023-11-03', '2023-11-04', '2023-11-05']
    store_id = '1234'
    
    for i, (ro, date) in enumerate(zip(ro_numbers, dates)):
        # Add some shop supplies values (not zero)
        shop_supply_value = 25.50 + (i * 10.25)  # Varying values: 25.50, 35.75, 46.00, etc.
        
        data.append({
            'ronumber': ro,
            'closeddate': date,
            'store_id': store_id,
            'totalshopsupply': shop_supply_value
        })
    
    return pd.DataFrame(data)

def create_mock_revenue_details():
    """Create mock revenue details data for testing"""
    data = []
    
    # Sample data matching the revenue details structure
    ro_numbers = ['987067', '994077', '995910', '996045', '996065']
    dates = ['2023-11-01', '2023-11-02', '2023-11-03', '2023-11-04', '2023-11-05']
    store_id = '1234'
    
    for i, (ro, date) in enumerate(zip(ro_numbers, dates)):
        data.append({
            'ronumber': ro,
            'closeddate': date,
            'store_id': store_id,
            'department': 'Service',
            'hide_ro': '0',
            'serviceadvisor': 'John Doe',
            'opcategory': 'Repair' if i % 2 == 0 else 'N/A',
            'lbrsale': 100.0 + (i * 20),
            'lbrsoldhours': 2.0 + i,
            'prtextendedsale': 50.0 + (i * 10),
            'prtextendedcost': 30.0 + (i * 5),
            'paytypegroup': 'C' if i < 3 else ('W' if i == 3 else 'I')
        })
    
    return pd.DataFrame(data)

def mock_calculate_shop_supplies_details(combined_revenue_details, total_details_df, advisor=None):
    """
    Mock version of calculate_shop_supplies_details that uses provided data instead of database
    """
    try:
        print("Starting shop supplies calculation...")
        
        # Step 1: Filter base data similar to temp_data_all_revenue_details
        base_filter = (
            (combined_revenue_details['department'] == 'Service') &
            (combined_revenue_details['hide_ro'] == '0')
        )
        
        # Apply advisor filter
        if advisor is None or advisor == {'all'}:
            advisor_filter = True
        else:
            advisor_filter = combined_revenue_details['serviceadvisor'].isin(advisor)
        
        temp_data = combined_revenue_details[base_filter & advisor_filter].copy()
        
        if temp_data.empty:
            print("No data after filtering")
            return {"combined": 0.0, "customer_pay": 0.0, "warranty": 0.0, "internal": 0.0, "month": "No Data"}
        
        print(f"Filtered data: {len(temp_data)} records")
        
        # Step 2: Create temp_all_revenue_details equivalent
        # First part: opcategory != 'N/A' and has some revenue/cost
        valid_ops = temp_data[
            (temp_data['opcategory'] != 'N/A') &
            ~((temp_data['lbrsale'].fillna(0) == 0) & 
              (temp_data['lbrsoldhours'].fillna(0) == 0) & 
              (temp_data['prtextendedsale'].fillna(0) == 0) & 
              (temp_data['prtextendedcost'].fillna(0) == 0)) &
            (temp_data['paytypegroup'].isin(['C', 'E', 'M', 'W', 'F', 'I']))
        ].copy()
        
        # Second part: opcategory == 'N/A' or all revenue/cost are zero
        invalid_ops = temp_data[
            (temp_data['opcategory'] == 'N/A') |
            ((temp_data['lbrsale'].fillna(0) == 0) & 
             (temp_data['lbrsoldhours'].fillna(0) == 0) & 
             (temp_data['prtextendedsale'].fillna(0) == 0) & 
             (temp_data['prtextendedcost'].fillna(0) == 0))
        ].copy()
        
        # Assign RO_Type based on paytypegroup
        valid_ops['RO_Type'] = valid_ops['paytypegroup'].map({
            'C': 'C', 'E': 'C', 'M': 'C',  # Customer pay
            'W': 'W', 'F': 'W',            # Warranty
            'I': 'I'                       # Internal
        })
        
        invalid_ops['RO_Type'] = 'I'  # All invalid ops are Internal
        
        # Combine both parts
        all_revenue_details = pd.concat([valid_ops, invalid_ops], ignore_index=True)
        
        print(f"Revenue details after processing: {len(all_revenue_details)} records")
        
        # Step 3: Update RO_Type based on priority (C > W > I)
        # Group by RO and determine the highest priority RO_Type
        ro_priority = all_revenue_details.groupby(['ronumber', 'closeddate', 'store_id'])['RO_Type'].apply(
            lambda x: 'C' if 'C' in x.values else ('W' if 'W' in x.values else 'I')
        ).reset_index()
        ro_priority.columns = ['ronumber', 'closeddate', 'store_id', 'Final_RO_Type']
        
        # Step 4: Get unique ROs with their final RO_Type
        unique_ros = ro_priority.drop_duplicates()
        
        print(f"Unique ROs: {len(unique_ros)} records")
        print("RO Type distribution:")
        print(unique_ros['Final_RO_Type'].value_counts())
        
        # Step 5: Join with shop supplies data
        if not total_details_df.empty:
            # Convert data types for proper joining
            total_details_df['ronumber'] = total_details_df['ronumber'].astype(str)
            total_details_df['store_id'] = total_details_df['store_id'].astype(str)
            total_details_df['closeddate'] = pd.to_datetime(total_details_df['closeddate'])
            
            # Ensure unique_ros has proper data types
            unique_ros['ronumber'] = unique_ros['ronumber'].astype(str)
            unique_ros['store_id'] = unique_ros['store_id'].astype(str)
            unique_ros['closeddate'] = pd.to_datetime(unique_ros['closeddate'])
            
            shop_supplies_data = pd.merge(
                total_details_df[['ronumber', 'closeddate', 'store_id', 'totalshopsupply']],
                unique_ros,
                on=['ronumber', 'closeddate', 'store_id'],
                how='inner'
            )
            
            print(f"Shop supplies data after join: {len(shop_supplies_data)} records")
            print(f"Total shop supplies values: {shop_supplies_data['totalshopsupply'].sum()}")
        else:
            print("Warning: No shop supplies data available. Using placeholder values.")
            shop_supplies_data = unique_ros.copy()
            shop_supplies_data['totalshopsupply'] = 0
        
        if shop_supplies_data.empty:
            print("No shop supplies data after join")
            return {"combined": 0.0, "customer_pay": 0.0, "warranty": 0.0, "internal": 0.0, "month": "No Data"}
        
        # Step 6: Convert closeddate to datetime and create month_year
        shop_supplies_data['closeddate'] = pd.to_datetime(shop_supplies_data['closeddate'])
        shop_supplies_data['month_year'] = shop_supplies_data['closeddate'].dt.strftime('%Y-%m')
        shop_supplies_data['month_first_day'] = shop_supplies_data['closeddate'].dt.to_period('M').dt.start_time
        
        # Step 7: Group by month and RO_Type, then sum shop supplies
        monthly_grouped = shop_supplies_data.groupby(['month_first_day', 'Final_RO_Type'])['totalshopsupply'].sum().reset_index()
        
        print("Monthly grouped data:")
        print(monthly_grouped)
        
        if monthly_grouped.empty:
            print("No monthly grouped data")
            return {"combined": 0.0, "customer_pay": 0.0, "warranty": 0.0, "internal": 0.0, "month": "No Data"}
        
        # Step 8: Pivot to get separate columns for each RO_Type
        monthly_pivot = monthly_grouped.pivot(index='month_first_day', columns='Final_RO_Type', values='totalshopsupply').fillna(0)
        
        print("Monthly pivot data:")
        print(monthly_pivot)
        
        # Ensure all required columns exist
        for col in ['C', 'W', 'I']:
            if col not in monthly_pivot.columns:
                monthly_pivot[col] = 0
        
        # Calculate combined totals
        monthly_pivot['Combined'] = monthly_pivot['C'] + monthly_pivot['W'] + monthly_pivot['I']
        
        # Sort by date descending (most recent first)
        monthly_pivot = monthly_pivot.sort_index(ascending=False)
        
        print("Final monthly pivot with combined:")
        print(monthly_pivot)
        
        # Return the most recent month's data
        if not monthly_pivot.empty:
            latest_month = monthly_pivot.iloc[0]
            result = {
                "combined": round(latest_month['Combined'], 2),
                "customer_pay": round(latest_month['C'], 2),
                "warranty": round(latest_month['W'], 2),
                "internal": round(latest_month['I'], 2),
                "month": monthly_pivot.index[0].strftime('%Y-%m')
            }
            print(f"Final result: {result}")
            return result
        
        return {"combined": 0.0, "customer_pay": 0.0, "warranty": 0.0, "internal": 0.0, "month": "No Data"}
        
    except Exception as e:
        print(f"Error calculating shop supplies details: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"combined": 0.0, "customer_pay": 0.0, "warranty": 0.0, "internal": 0.0, "month": "No Data"}

def main():
    """Test the shop supplies calculation"""
    print("=== Testing Shop Supplies Calculation Fix ===\n")
    
    # Create mock data
    print("1. Creating mock data...")
    revenue_details = create_mock_revenue_details()
    total_details = create_mock_total_details()
    
    print(f"Revenue details: {len(revenue_details)} records")
    print(f"Total details: {len(total_details)} records")
    
    print("\nRevenue details sample:")
    print(revenue_details.head())
    
    print("\nTotal details sample:")
    print(total_details.head())
    
    # Test the calculation
    print("\n2. Testing shop supplies calculation...")
    result = mock_calculate_shop_supplies_details(revenue_details, total_details, advisor={'all'})
    
    print(f"\n3. Final Result:")
    print(json.dumps(result, indent=2))
    
    # Verify the result is not all zeros
    if result['combined'] > 0:
        print("\n✅ SUCCESS: Shop supplies calculation is working! Values are no longer zero.")
        print(f"   Combined: ${result['combined']}")
        print(f"   Customer Pay: ${result['customer_pay']}")
        print(f"   Warranty: ${result['warranty']}")
        print(f"   Internal: ${result['internal']}")
    else:
        print("\n❌ ISSUE: Shop supplies values are still zero. Need to investigate further.")

if __name__ == "__main__":
    main()
